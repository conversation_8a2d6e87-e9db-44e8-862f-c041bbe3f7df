# Flatmate Application - Issues Log

## Current Issues

### 0. Database Not Updating
- **Description**: Processed transaction data is not being saved to the database
- **Location**: File processing pipeline to database write operations
- **Impact**: Critical - No data persistence despite successful file processing
- **Evidence**:
  - Files are being processed successfully
  - No errors during processing
  - Database remains empty after processing
  - Logs show no database write operations
- **Potential Causes**:
  - Missing database write calls after processing
  - Silent failures in database operations
  - Transactions not being committed
  - Incorrect database connection handling

### 1. UI Rendering Issues
- **Description**: Multiple QPainter-related warnings in the console
- **Location**: Occurs during module transitions
- **Error Examples**:
  ```
  QPainter::begin: Paint device returned engine == 0, type: 3
  QPainter::setCompositionMode: Painter not active
  QPainter::fillRect: Painter not active
  ```
- **Impact**: May cause visual artifacts or unresponsive UI elements

### 2. UpdateDataStatusBar Deletion Error
- **Description**: Attempting to access deleted C++ object
- **Location**: Occurs during file processing
- **Error Example**:
  ```
  Error in event listener for info_message: Internal C++ object (UpdateDataStatusBar) already deleted.
  ```
- **Impact**: Status bar updates may fail, potential memory issues

### 3. Signal Disconnection Warning
- **Description**: Failed to disconnect signal in home_view.py
- **Location**: Line 253 in home_view.py
- **Error Example**:
  ```
  C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\home\home_view.py:253: RuntimeWarning: Failed to disconnect (None) from signal "view_data_clicked()".
  ```
- **Impact**: Potential memory leaks or signal handling issues

### 4. Missing Handler for fmMaster.csv
- **Description**: No matching handler found for fmMaster.csv file
- **Location**: File processing pipeline
- **Impact**: File cannot be processed
- **File Details**:
  - Size: 324.8 KB
  - Format: CSV with 17 columns

## Next Steps
1. Investigate and fix the QPainter initialization sequence
2. Review UpdateDataStatusBar lifecycle management
3. Fix signal disconnection in home_view.py
4. Create a handler for fmMaster.csv format

## Investigation Notes
- Database location: `C:\Users\<USER>\.flatmate\data\transactions.db`
- Application successfully processed Kiwibank statement files
- Module transitions are working but with some cleanup warnings

## Priority
0. **CRITICAL**: Database not updating (data loss)
1. Critical: UpdateDataStatusBar deletion error (potential crashes)
2. High: Signal disconnection warning (memory management)
3. Medium: UI rendering issues (user experience)
4. Low: Missing fmMaster.csv handler (feature enhancement)
