# Flatmate Column System Unification: Detailed Implementation Plan

*Date: 2025-06-27*

This document outlines the detailed implementation plan for unifying the Flatmate column system, based on comprehensive analysis of the current codebase and requirements.

## 1. Overview

The plan aims to create a single, unified column system that:
- Serves as the canonical source of truth for all column definitions
- Provides rich metadata for each column
- Supports module-specific defaults and behaviors
- Maintains backward compatibility
- Enables gradual migration

## 2. Implementation Phases

### Phase 1: Enhanced Column Definition System (2 weeks)

#### 1.1 Create Enhanced Column Class

Create a rich metadata column class in `fm/core/standards/column_definition.py`:

```python
from dataclasses import dataclass
from enum import Enum
from typing import Dict, Optional, Any, Type

@dataclass
class ColumnDefinition:
    """Rich metadata column definition."""
    db_name: str  # Database name (snake_case)
    display_name: str  # Display name (Title Case)
    data_type: Type  # Python type (str, float, int, etc.)
    width: int = 20  # Default width in characters
    editable: bool = False  # Whether column is editable in UI
    required: bool = False  # Whether column is required for validation
    visible_by_default: bool = True  # Whether column is visible by default
    module_visibility: Dict[str, bool] = None  # Module-specific visibility
    description: Optional[str] = None  # Description for documentation
    format_spec: Optional[str] = None  # Format specification (e.g., date format)
    validation_rules: Optional[Dict[str, Any]] = None  # Validation rules
    
    def __post_init__(self):
        """Initialize default values."""
        if self.module_visibility is None:
            self.module_visibility = {}
```

#### 1.2 Enhance StandardColumns Enum

Update `fm/core/standards/standard_columns.py` to use the enhanced column definition:

```python
from enum import Enum, auto
from .column_definition import ColumnDefinition
import datetime

class StandardColumns(Enum):
    """Enhanced standard columns with rich metadata."""
    
    # Core transaction columns
    DATE = ColumnDefinition(
        db_name="date",
        display_name="Date",
        data_type=datetime.date,
        width=12,
        required=True,
        description="Transaction date"
    )
    
    DETAILS = ColumnDefinition(
        db_name="details",
        display_name="Details",
        data_type=str,
        width=40,
        required=True,
        description="Transaction details/description"
    )
    
    AMOUNT = ColumnDefinition(
        db_name="amount",
        display_name="Amount",
        data_type=float,
        width=12,
        required=True,
        description="Transaction amount"
    )
    
    # Add all other columns with appropriate metadata
    # ...
    
    @classmethod
    def get_db_name(cls, column):
        """Get database name for a column."""
        return column.value.db_name
    
    @classmethod
    def get_display_name(cls, column):
        """Get display name for a column."""
        return column.value.display_name
    
    @classmethod
    def from_db_name(cls, db_name):
        """Get enum member from database name."""
        for column in cls:
            if column.value.db_name == db_name:
                return column
        return None
    
    @classmethod
    def get_all_db_names(cls):
        """Get all database column names."""
        return [col.value.db_name for col in cls]
    
    @classmethod
    def get_all_display_names(cls):
        """Get all display column names."""
        return [col.value.display_name for col in cls]
    
    @classmethod
    def get_standard_column_widths(cls):
        """Get standard column widths as {display_name: width} dict."""
        return {col.value.display_name: col.value.width for col in cls}
    
    @classmethod
    def get_required_columns(cls):
        """Get required column db_names."""
        return [col.value.db_name for col in cls if col.value.required]
    
    @classmethod
    def get_default_visible_columns_for_module(cls, module_name):
        """Get default visible columns for a specific module."""
        # Module-specific logic
        if module_name == 'categorize':
            return [
                cls.DATE.value.db_name,
                cls.DETAILS.value.db_name,
                cls.AMOUNT.value.db_name,
                cls.ACCOUNT.value.db_name,
                cls.TAGS.value.db_name,
                cls.CATEGORY.value.db_name
            ]
        # Add other modules...
        return [col.value.db_name for col in cls 
                if col.value.module_visibility.get(module_name, col.value.visible_by_default)]
```

#### 1.3 Create Adapter for Backward Compatibility

Create an adapter in `fm/core/standards/fm_standard_columns_adapter.py` to maintain backward compatibility:

```python
"""
Adapter for backward compatibility with fm_standard_columns.py.
This module provides the same interface as the original fm_standard_columns.py,
but delegates to the new StandardColumns enum.
"""

from .standard_columns import StandardColumns

# Re-export the enum for backward compatibility
class LegacyStandardColumns(StandardColumns):
    """Legacy StandardColumns enum for backward compatibility."""
    pass

# Export as the original name for backward compatibility
StandardColumns = LegacyStandardColumns
```

### Phase 2: Enhanced Column Manager (2 weeks)

#### 2.1 Update Column Manager

Enhance `fm/core/data_services/column_manager.py` to use the new StandardColumns:

```python
from typing import Dict, List, Optional
import pandas as pd
from fm.core.data_services.standards.standard_columns import StandardColumns

class ColumnManager:
    """Enhanced column manager with rich metadata support."""
    
    @classmethod
    def get_display_mapping(cls, df_columns: List[str], 
                          custom_mapping: Optional[Dict[str, str]] = None) -> Dict[str, str]:
        """Get mapping from db_names to display_names."""
        mapping = {}
        
        for db_col in df_columns:
            if custom_mapping and db_col in custom_mapping:
                # Custom override takes precedence
                mapping[db_col] = custom_mapping[db_col]
            else:
                # Use StandardColumns if available
                column = StandardColumns.from_db_name(db_col)
                if column:
                    mapping[db_col] = column.value.display_name
                else:
                    # Fallback for unknown columns - convert snake_case to Title Case
                    mapping[db_col] = db_col.replace('_', ' ').title()
        
        return mapping
    
    @classmethod
    def get_reverse_mapping(cls, df_columns: List[str], 
                          custom_mapping: Optional[Dict[str, str]] = None) -> Dict[str, str]:
        """Get mapping from display_names to db_names."""
        display_mapping = cls.get_display_mapping(df_columns, custom_mapping)
        return {display_name: db_name for db_name, display_name in display_mapping.items()}
    
    @classmethod
    def apply_display_names(cls, df: pd.DataFrame,
                          custom_mapping: Optional[Dict[str, str]] = None) -> pd.DataFrame:
        """Apply display names to DataFrame columns."""
        if df.empty:
            return df.copy()

        display_mapping = cls.get_display_mapping(
            df.columns.tolist(),
            custom_mapping
        )

        display_df = df.copy()
        display_df.columns = [display_mapping.get(col, col) for col in df.columns]
        return display_df
    
    @classmethod
    def apply_db_names(cls, df: pd.DataFrame, 
                      custom_mapping: Optional[Dict[str, str]] = None) -> pd.DataFrame:
        """Apply database names to DataFrame columns."""
        if df.empty:
            return df.copy()
            
        # Get reverse mapping (display_name -> db_name)
        reverse_mapping = cls.get_reverse_mapping(
            StandardColumns.get_all_db_names(),
            custom_mapping
        )
        
        db_df = df.copy()
        db_df.columns = [reverse_mapping.get(col, col.lower().replace(' ', '_')) 
                         for col in df.columns]
        return db_df
    
    @classmethod
    def get_default_visible_columns_for_module(cls, module_name: str) -> List[str]:
        """Get default visible columns for a module."""
        return StandardColumns.get_default_visible_columns_for_module(module_name)
    
    @classmethod
    def convert_transactions_to_dataframe(cls, transactions: List, 
                                        ensure_columns: Optional[List[str]] = None) -> pd.DataFrame:
        """Convert transaction objects to DataFrame with db_names as columns."""
        # Implementation remains similar but uses new StandardColumns
        # ...
```

#### 2.2 Update Column Name Service

Update `fm/core/data_services/column_name_service.py` to delegate to ColumnManager:

```python
"""
DEPRECATED: Centralized column name mapping service.

This service has been deprecated in favor of ColumnManager.
This module now delegates to ColumnManager for backward compatibility.
"""

import pandas as pd
from typing import Dict, List, Optional
from .column_manager import ColumnManager

class ColumnNameService:
    """
    DEPRECATED: Centralized column name mapping service.
    
    This class now delegates to ColumnManager for backward compatibility.
    """
    
    @staticmethod
    def get_display_mapping(df_columns: List[str], 
                          custom_mapping: Optional[Dict[str, str]] = None) -> Dict[str, str]:
        """DEPRECATED: Use ColumnManager.get_display_mapping() instead."""
        return ColumnManager.get_display_mapping(df_columns, custom_mapping)
    
    # Add all other methods delegating to ColumnManager
    # ...
```

### Phase 3: UI Component Updates (3 weeks)

#### 3.1 Update Table View System

Update `fm/gui/_shared_components/table_view_v2/fm_table_view.py` to use the enhanced ColumnManager:

```python
# Update imports
from fm.core.data_services.column_manager import ColumnManager

class CustomTableView_v2(QWidget):
    # ...
    
    def set_dataframe(self, df: pd.DataFrame, custom_column_names: Optional[Dict[str, str]] = None):
        """Set the table data from a pandas DataFrame with automatic column name handling."""
        # Store original DataFrame with db_names for reference
        self._dataframe_db = df.copy()
        
        # Convert db_names to display_names for UI using enhanced ColumnManager
        display_df = ColumnManager.apply_display_names(df, custom_column_names)
        
        # Set the display DataFrame to the table view
        self.table_view.set_dataframe(display_df)
        
        # ...
```

#### 3.2 Update Transaction View Panel

Update `fm/modules/categorize/_view/components/center_panel/transaction_view_panel.py` to use the enhanced ColumnManager:

```python
# Update imports
from fm.core.data_services.column_manager import ColumnManager

class TransactionViewPanel(QWidget):
    # ...
    
    def set_transactions(self, df: pd.DataFrame):
        """Set the transactions dataframe to display."""
        if df is not None and not df.empty:
            # Get standard column widths from ColumnManager
            standard_widths = ColumnManager.get_standard_column_widths()
            
            # Configure table
            self.transaction_table.configure(
                auto_size_columns=True,
                max_column_width=40,
                column_widths=standard_widths,
                editable_columns=['Tags'],
                show_toolbar=True
            ).set_dataframe(df).show()
```

### Phase 4: Database Integration (2 weeks)

#### 4.1 Update Transaction Repository

Update `fm/core/data_services/repositories/sqlite_transaction_repository.py` to use the enhanced StandardColumns:

```python
from fm.core.data_services.standards.standard_columns import StandardColumns

class SQLiteTransactionRepository(TransactionRepository):
    # ...
    
    def add_transactions_from_df(self, df: pd.DataFrame, source_file: str = None):
        """Add transactions from DataFrame to database."""
        # Ensure required columns
        required_columns = [col.value.db_name for col in StandardColumns if col.value.required]
        for col in required_columns:
            if col not in df.columns:
                raise ValueError(f"Required column '{col}' missing from DataFrame")
        
        # Add metadata columns
        # ...
```

#### 4.2 Update Schema Creation

Update database schema creation to use column metadata:

```python
def create_schema(self):
    """Create database schema using StandardColumns metadata."""
    columns = []
    
    # Map Python types to SQLite types
    type_mapping = {
        str: "TEXT",
        int: "INTEGER",
        float: "REAL",
        bool: "INTEGER",  # SQLite has no boolean type
        datetime.date: "TEXT",
        datetime.datetime: "TEXT",
    }
    
    # Add columns based on StandardColumns metadata
    for col in StandardColumns:
        col_def = col.value
        sql_type = type_mapping.get(col_def.data_type, "TEXT")
        
        # Add column definition
        nullable = "" if col_def.required else " NULL"
        columns.append(f"{col_def.db_name} {sql_type}{nullable}")
    
    # Create table
    columns_sql = ", ".join(columns)
    self._execute(f"CREATE TABLE IF NOT EXISTS transactions ({columns_sql})")
```

### Phase 5: Update Data Module Integration (2 weeks)

#### 5.1 Update Data Pipeline

Update `fm/modules/update_data/utils/dw_pipeline.py` to use the enhanced StandardColumns:

```python
from fm.core.data_services.standards.standard_columns import StandardColumns

def validate_data_structure(df):
    """Validate DataFrame structure using StandardColumns metadata."""
    # Check required columns
    required_columns = [col.value.db_name for col in StandardColumns if col.value.required]
    for col in required_columns:
        if col not in df.columns:
            raise ValueError(f"Required column '{col}' missing from DataFrame")
    
    # Validate data types
    for col in StandardColumns:
        col_def = col.value
        if col_def.db_name in df.columns:
            # Implement type validation based on col_def.data_type
            # ...
```

#### 5.2 Update Statement Handlers

Update statement handlers to use the enhanced StandardColumns:

```python
from fm.core.data_services.standards.standard_columns import StandardColumns

class BaseStatementHandler:
    """Base class for statement handlers."""
    
    def map_columns(self, df):
        """Map source columns to standard columns."""
        # Implementation using enhanced StandardColumns
        # ...
```

### Phase 6: Documentation and Testing (2 weeks)

#### 6.1 Update Documentation

Create comprehensive documentation for the new column system:

- Architecture overview
- Migration guide for developers
- API reference
- Examples and best practices

#### 6.2 Create Tests

Create comprehensive tests for the new column system:

- Unit tests for StandardColumns
- Unit tests for ColumnManager
- Integration tests for UI components
- End-to-end tests for database operations

## 3. Migration Strategy

### 3.1 Gradual Migration Approach

1. **Dual Operation Period**:
   - Both old and new systems operate in parallel
   - New code uses new system
   - Old code uses adapter layer

2. **Component-by-Component Migration**:
   - Update components one by one
   - Start with core services, then UI components
   - End with database and data pipeline

3. **Deprecation Notices**:
   - Add deprecation notices to old APIs
   - Provide clear migration path in documentation
   - Set timeline for complete migration

### 3.2 Backward Compatibility

1. **Adapter Layer**:
   - Create adapters for backward compatibility
   - Ensure old code continues to work
   - Gradually phase out adapters

2. **Method Signatures**:
   - Maintain existing method signatures
   - Add new methods with enhanced functionality
   - Document migration path

3. **Configuration Keys**:
   - Maintain existing configuration keys
   - Add new keys for enhanced functionality
   - Provide migration utilities

## 4. Timeline and Milestones

### Milestone 1: Enhanced Column Definition (End of Week 2)
- Enhanced ColumnDefinition class
- Updated StandardColumns enum
- Adapter for backward compatibility

### Milestone 2: Enhanced Column Manager (End of Week 4)
- Updated ColumnManager
- Updated ColumnNameService
- Initial tests

### Milestone 3: UI Component Updates (End of Week 7)
- Updated Table View System
- Updated Transaction View Panel
- UI tests

### Milestone 4: Database Integration (End of Week 9)
- Updated Transaction Repository
- Updated Schema Creation
- Database tests

### Milestone 5: Update Data Module Integration (End of Week 11)
- Updated Data Pipeline
- Updated Statement Handlers
- Integration tests

### Milestone 6: Documentation and Final Testing (End of Week 13)
- Comprehensive documentation
- Complete test suite
- Migration guide

## 5. Risks and Mitigations

### 5.1 Backward Compatibility Risks

**Risk**: Breaking changes to existing code
**Mitigation**: Thorough testing, adapter layer, gradual migration

### 5.2 Performance Risks

**Risk**: Performance degradation with enhanced metadata
**Mitigation**: Profiling, optimization, caching

### 5.3 Data Integrity Risks

**Risk**: Data corruption during migration
**Mitigation**: Comprehensive tests, data validation, backup strategy

## 6. Conclusion

This implementation plan provides a comprehensive roadmap for unifying the Flatmate column system. By following this plan, we can create a single, metadata-rich column system that meets the needs of all components while maintaining backward compatibility and enabling gradual migration.

The unified column system will provide:
- A single source of truth for column definitions
- Rich metadata for each column
- Module-specific defaults and behaviors
- Backward compatibility
- Clear, discoverable API
- Comprehensive documentation and tests

This will result in a more maintainable, extensible, and user-friendly application.
