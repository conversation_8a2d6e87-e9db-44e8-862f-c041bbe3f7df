# Timed Processes (Longest to Shortest)

| Process Name                                  | Duration (s) |
|-----------------------------------------------|--------------|
| CategorizePresenter._handle_load_db           | 4.299        |
| Table View Data Setting                       | 3.981        |
| Transaction Categorization                    | 0.157        |
| Data Retrieval from Cache                     | 0.060        |
| TransactionViewPanel._init_ui                 | 0.039        |


# processes: Timed Processes (Longest to Shortest)------------------------------------------------



# | CategorizePresenter._handle_load_db           | 4.299 
## Analysis

This is the main database loading and preparation routine for the Categorize module. It:
- Fetches all transactions from the database (optionally filtered)
- Applies transaction categorisation to every row
- Sorts the results and sets up the data for the UI
- Triggers progress updates and completion notifications

This is the slowest process because it combines database access, DataFrame operations, and per-row categorisation (which scales linearly with transaction count). Optimisation should focus on:
- Improving database query speed
- Profiling and optimising the categorisation loop
- Ensuring DataFrame operations are vectorised where possible

Typical for first-load or large datasets; subsequent UI actions should be faster due to caching.



# | Table View Data Setting                       | 3.981        



# | Transaction Categorization                    | 0.157        |



# | Data Retrieval from Cache                     | 0.060        |



# | TransactionViewPanel._init_ui                 | 0.039        |

