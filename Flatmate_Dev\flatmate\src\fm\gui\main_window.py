#!/usr/bin/env python3
"""
Main window implementation for the FlatMate application.
"""

# Standard library imports
from pathlib import Path

# Third-party imports
from PySide6.QtCore import QEasingCurve, QParallelAnimationGroup, QPropertyAnimation, Qt, QPoint
from PySide6.QtWidgets import (
    QGraphicsOpacityEffect,
    QSizePolicy,
    Q<PERSON>plitter,
    QVBoxLayout,
    QWidget,
    QLabel,
    QHBoxLayout,
    QFrame,
)
from PySide6.QtGui import QPixmap, QFont

# PyQt-Frameless-Window imports - TEMPORARILY DISABLED DUE TO SYSTEM DPI ISSUES
# try:
#     from qframelesswindow import FramelessWindow
#     FRAMELESS_AVAILABLE = True
# except ImportError:
#     print("WARNING: PySide6-Frameless-Window not installed. Using fallback QMainWindow.")
#     from PySide6.QtWidgets import QMainWindow as FramelessWindow
#     FRAMELESS_AVAILABLE = False

# TEMPORARY: Force fallback to QMainWindow to restore system DPI
print("INFO: Using QMainWindow fallback to avoid DPI system issues.")
from PySide6.QtWidgets import QMainWindow as FramelessWindow
FRAMELESS_AVAILABLE = False

# Module coordinator for handling module interactions
from ..module_coordinator import ModuleCoordinator

# Local imports
from ._main_window_components.info_bar.info_bar import InfoBar
from ._main_window_components.right_side_bar._right_side_bar_manager import (
    RightSideBarManager,
)
from ._main_window_components.title_bar.custom_title_bar import CustomTitleBar
from .config.gui_config import gui_config
from .config.gui_keys import GuiKeys


class MainWindow(FramelessWindow):
    """Main application window with frameless window support."""

    def __init__(self):
        """Initialize main window."""
        # FramelessWindow handles all the frameless window setup
        super().__init__()

        # Set window properties
        self.setWindowTitle("flatmate")
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)

        # Initialize animation properties
        self.left_panel_animation = None
        self.right_panel_animation = None

        # Load panel size configuration
        self._load_panel_sizes()

        # Set up the user interface
        self.setup_ui()

        # Apply any window-specific styles
        self._apply_window_styles()

        # Set up the centralized info bar (after UI is created)
        self._setup_info_bar()

        # Module manager will be set externally from main.py
        self.module_manager = None

    def _load_panel_sizes(self):
        """Load panel sizes from configuration."""
        # Get minimum width for panels
        min_width = gui_config.get_value("gui.window.min_panel_width", default=240)

        # Load left panel size
        self.stored_left_size = max(
            min_width,
            gui_config.get_value(
                GuiKeys.Panel.LEFT_LAST_WIDTH,
                default=gui_config.get_value(GuiKeys.Panel.LEFT_DEFAULT_WIDTH),
            ),
        )

        # Load right panel size
        self.stored_right_size = max(
            min_width,
            gui_config.get_value(
                GuiKeys.Panel.RIGHT_LAST_WIDTH,
                default=gui_config.get_value(GuiKeys.Panel.RIGHT_DEFAULT_WIDTH),
            ),
        )

    def setup_ui(self):
        """Set up the user interface."""
        # Configure window dimensions
        self._setup_window_dimensions()

        # Create central widget and main layout
        self._setup_main_layout()

        # Create and configure the main splitter
        self._setup_splitter()

        # Set up all panels
        self._setup_panels()

        # Configure the right side bar
        self._setup_right_side_bar()

        # Add panels to splitter and configure sizes
        self._configure_splitter_layout()

        # Initialize panel visibility
        self._initialize_panel_visibility()

    def _setup_window_dimensions(self):
        """Configure the window dimensions."""
        width = gui_config.get_value(GuiKeys.Window.WIDTH)
        height = gui_config.get_value(GuiKeys.Window.HEIGHT)
        self.resize(width, height)
        self.setMinimumSize(800, 600)

    def _setup_info_bar(self):
        """Set up the centralized info bar with container approach."""
        # Create a container for center panel content + info bar
        self.center_container = QWidget()
        self.center_container_layout = QVBoxLayout(self.center_container)
        self.center_container_layout.setContentsMargins(0, 0, 0, 0)
        self.center_container_layout.setSpacing(0)

        # Create and add info bar (will be at bottom, hidden by default)
        self.info_bar = InfoBar(self)
        self.info_bar.setVisible(False)  # Hidden by default
        self.center_container_layout.addWidget(self.info_bar)

        # Replace the center panel's layout content with our container
        self.center_layout.addWidget(self.center_container)

    def show_info_bar(self):
        """Show the centralized info bar."""
        if hasattr(self, 'info_bar'):
            self.info_bar.setVisible(True)

    def hide_info_bar(self):
        """Hide the centralized info bar."""
        if hasattr(self, 'info_bar'):
            self.info_bar.setVisible(False)

    def _apply_window_styles(self):
        """Apply window-specific styles."""
        # Set style for the main window
        self.setStyleSheet("""
            QMainWindow {
                background-color: #252526;
            }
        """)
        
    def resizeEvent(self, event):
        """Handle window resize events."""
        if not self.isMaximized() and not self.isFullScreen():
            # Save window size when manually resized
            gui_config.set_window_size(self.width(), self.height())
        super().resizeEvent(event)
        
    # Window position saving is not currently implemented in GuiConfig
    # Will be added in a future update if needed

    # ---------- Layout helpers ----------

    # ---------- Layout helpers (Option B refactor) ----------
    def _setup_main_layout(self):
        """Set up the central widget and main layout."""
        if FRAMELESS_AVAILABLE:
            # For FramelessWindow, set layout directly on the window
            self.main_layout = QVBoxLayout(self)
            self.central_widget = self  # Reference for compatibility
        else:
            # For QMainWindow fallback, use central widget
            self.central_widget = QWidget()
            self.central_widget.setObjectName("central_widget")
            self.setCentralWidget(self.central_widget)
            self.main_layout = QVBoxLayout(self.central_widget)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)
        '''# TODO: Sort out custom title bar / frameless mess, one system or the other '''
        # Add custom title bar
        #self.title_bar = self._create_title_bar() # ! NOTE: create CustomTItleBar Commented out for now 
        #self.main_layout.addWidget(self.title_bar)
        
        # Container for the main content (below title bar)
        self.content_container, self.content_layout = self._create_content_area()
        self.main_layout.addWidget(self.content_container, 1)  # Add stretch factor to make it take remaining space
        
        # Initialize the splitter
        self.splitter = None

    def _create_title_bar(self):
        """Create and return the CustomTitleBar widget."""
        return CustomTitleBar(self)

    def _create_content_area(self):
        """Create content container + layout and return both."""
        content_container = QWidget()
        content_container.setObjectName("content_container")
        content_layout = QVBoxLayout(content_container)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(0)
        return content_container, content_layout

    def _setup_splitter(self):
        """Set up the main splitter."""
        # Create splitter
        self.splitter = QSplitter(Qt.Orientation.Horizontal)
        self.splitter.setChildrenCollapsible(False)
        self.splitter.setHandleWidth(1)  # Thin splitter handle
        self.splitter.setStyleSheet("QSplitter::handle { background: #2D2D2D; }")
        
        # Add the splitter to the content container
        self.content_layout.addWidget(self.splitter)

        # Connect splitter moved signal to save sizes
        self.splitter.splitterMoved.connect(self._save_panel_sizes)

    def _setup_panels(self):
        """Set up the left, center, and right panels."""
        # Left panel
        self.left_panel = QWidget()
        self.left_panel.setObjectName("left_panel")
        self.left_panel.setMinimumWidth(self.stored_left_size)  # Force minimum width
        self.left_layout = QVBoxLayout(self.left_panel)
        self.left_layout.setContentsMargins(6, 8, 6, 8)  # Reduced from 10,30,10,30 to 6,8,6,8
        self.left_layout.setSpacing(20)  # Reduced from 25 to 20

        # Left panel opacity effect
        self.left_panel_opacity = QGraphicsOpacityEffect(self.left_panel)
        self.left_panel_opacity.setOpacity(1)
        self.left_panel.setGraphicsEffect(self.left_panel_opacity)

        # Center panel
        self.center_panel = QWidget()
        self.center_panel.setObjectName("center_panel")
        self.center_layout = QVBoxLayout(self.center_panel)
        self.center_layout.setContentsMargins(
            4, 4, 4, 4
        )  # Reduced from 20,20,20,10 to 4,4,4,4 for better space utilization
        self.center_layout.setSpacing(0)

        # Right panel
        self.right_panel = QWidget()
        self.right_panel.setObjectName("right_panel")
        self.right_panel.setMinimumWidth(self.stored_right_size)  # Force minimum width
        self.right_layout = QVBoxLayout(self.right_panel)
        self.right_layout.setContentsMargins(6, 8, 6, 8)  # Reduced from 10,30,10,30 to 6,8,6,8
        self.right_layout.setSpacing(20)  # Reduced from 25 to 20
        # awfully complex)#
        # Right panel opacity effect
        self.right_panel_opacity = QGraphicsOpacityEffect(self.right_panel)
        self.right_panel_opacity.setOpacity(1)
        self.right_panel.setGraphicsEffect(self.right_panel_opacity)

    def _setup_right_side_bar(self):
        """Set up the right side bar for navigation."""
        # Create right side bar widget
        self.right_side_bar = QWidget()
        self.right_side_bar.setObjectName("right_side_bar")

        # Set size policy to make the side bar conform to its contents
        self.right_side_bar.setSizePolicy(
            QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Preferred
        )

        # Create and prepare the layout
        self.right_side_bar_layout = QVBoxLayout(self.right_side_bar)
        self.right_side_bar_layout.setContentsMargins(0, 4, 0, 0)  # Reduced top margin from 10 to 4
        self.right_side_bar_layout.setSpacing(0)
        self.right_side_bar_layout.setAlignment(Qt.AlignmentFlag.AlignTop)

        # Initialize the right side bar manager
        self._initialize_right_side_bar_manager()

        # Set up opacity effect for the right side bar
        self.right_side_bar_opacity = QGraphicsOpacityEffect(self.right_side_bar)
        self.right_side_bar_opacity.setOpacity(1)
        self.right_side_bar.setGraphicsEffect(self.right_side_bar_opacity)

    def _initialize_right_side_bar_manager(self):
        """Initialize the right side bar manager and connect its signals."""
        # First, clear any existing layout to avoid conflicts
        if self.right_side_bar.layout():
            # We need to properly remove the old layout
            old_layout = self.right_side_bar.layout()
            while old_layout.count():
                item = old_layout.takeAt(0)
                if item.widget():
                    item.widget().setParent(None)
            # Reparent the old layout to a temporary widget to delete it
            QWidget().setLayout(old_layout)

        # Now create our new layout with zero margins
        self.right_side_bar_layout = QVBoxLayout(self.right_side_bar)
        self.right_side_bar_layout.setContentsMargins(0, 0, 0, 0)  # Remove all margins
        self.right_side_bar_layout.setSpacing(0)
        self.right_side_bar_layout.setAlignment(Qt.AlignmentFlag.AlignTop)

        # Initialize the manager with our prepared widget
        self.right_side_bar_manager = RightSideBarManager(self.right_side_bar)

    def _configure_splitter_layout(self):
        """Configure the splitter layout and add panels."""
        # Add panels to splitter
        self.splitter.addWidget(self.left_panel)
        self.splitter.addWidget(self.center_panel)
        self.splitter.addWidget(self.right_panel)
        self.splitter.addWidget(self.right_side_bar)

        # Set stretch factor for center panel
        self.splitter.setStretchFactor(1, 1)

        # Calculate available width (accounting for the title bar)
        available_width = self.content_container.width()
        
        # Let the right side bar determine its own size based on its contents
        right_side_bar_width = self.right_side_bar.sizeHint().width()

        # Set initial sizes
        sizes = [
            self.stored_left_size,
            available_width - (self.stored_left_size + self.stored_right_size + right_side_bar_width),
            self.stored_right_size,
            right_side_bar_width,
        ]
        self.splitter.setSizes(sizes)

        # Prevent panels from collapsing below minimum width
        self.splitter.setCollapsible(0, False)  # Left panel
        self.splitter.setCollapsible(2, False)  # Right panel
        self.splitter.setCollapsible(3, False)  # Right side bar

    def _initialize_panel_visibility(self):
        """Initialize the visibility of panels."""
        # Set initial panel visibility
        self.left_panel.show()
        self.right_panel.hide()
        self.right_side_bar.show()  # Right side bar is always visible by default

        # Module manager will be set externally from main.py
        self.module_manager = None

    def set_module_manager(self, module_manager):
        """Set the module manager instance.

        This allows us to maintain a single ModuleCoordinator instance
        that's properly initialized from main.py.

        Args:
            module_manager: The ModuleCoordinator instance
        """
        self.module_manager = module_manager
        
        # Connect the right side bar's navigation signals to the module coordinator
        # This enables navigation from the NavPane to trigger module transitions
        if hasattr(self, 'right_side_bar_manager'):
            print("Connecting right side bar navigation signals to module coordinator")
            self.right_side_bar_manager.navigationSelected.connect(self.module_manager.transition_to)

    def show_left_panel(self):
        """Show left panel with animation."""
        if self.left_panel_animation:
            self.left_panel_animation.stop()

        self.left_panel.show()
        animation_group = QParallelAnimationGroup(self)

        # Width animation
        width_anim = QPropertyAnimation(self.left_panel, b"minimumWidth")
        width_anim.setDuration(250)
        width_anim.setStartValue(0)
        width_anim.setEndValue(self.stored_left_size)
        width_anim.setEasingCurve(QEasingCurve.Type.OutCubic)

        # Opacity animation
        opacity_anim = QPropertyAnimation(self.left_panel_opacity, b"opacity")
        opacity_anim.setDuration(250)
        opacity_anim.setStartValue(0.0)
        opacity_anim.setEndValue(1.0)
        opacity_anim.setEasingCurve(QEasingCurve.Type.OutCubic)

        animation_group.addAnimation(width_anim)
        animation_group.addAnimation(opacity_anim)
        self.left_panel_animation = animation_group
        animation_group.start()

    def hide_left_panel(self):
        """Hide left panel with animation."""
        if self.left_panel_animation:
            self.left_panel_animation.stop()

        # Store current width for later
        self.stored_left_size = self.left_panel.width()

        animation_group = QParallelAnimationGroup(self)

        # Width animation
        width_anim = QPropertyAnimation(self.left_panel, b"minimumWidth")
        width_anim.setDuration(250)
        width_anim.setStartValue(self.left_panel.width())
        width_anim.setEndValue(0)
        width_anim.setEasingCurve(QEasingCurve.Type.OutCubic)

        # Opacity animation
        opacity_anim = QPropertyAnimation(self.left_panel_opacity, b"opacity")
        opacity_anim.setDuration(250)
        opacity_anim.setStartValue(1.0)
        opacity_anim.setEndValue(0.0)
        opacity_anim.setEasingCurve(QEasingCurve.Type.OutCubic)

        animation_group.addAnimation(width_anim)
        animation_group.addAnimation(opacity_anim)
        animation_group.finished.connect(self.left_panel.hide)

        self.left_panel_animation = animation_group
        animation_group.start()

    def toggle_left_panel(self):
        """Toggle the left panel visibility."""
        if self.left_panel.isVisible():
            self.hide_left_panel()
        else:
            self.show_left_panel()

    def show_right_panel(self):
        """Show right panel with animation."""
        if self.right_panel_animation:
            self.right_panel_animation.stop()

        # Use the stored size for the panel
        target_width = self.stored_right_size

        self.right_panel.show()
        animation_group = QParallelAnimationGroup(self)

        # Width animation
        width_anim = QPropertyAnimation(self.right_panel, b"minimumWidth")
        width_anim.setDuration(250)
        width_anim.setStartValue(0)
        width_anim.setEndValue(target_width)
        width_anim.setEasingCurve(QEasingCurve.Type.OutCubic)

        # Opacity animation
        opacity_anim = QPropertyAnimation(self.right_panel_opacity, b"opacity")
        opacity_anim.setDuration(250)
        opacity_anim.setStartValue(0.0)
        opacity_anim.setEndValue(1.0)
        opacity_anim.setEasingCurve(QEasingCurve.Type.OutCubic)

        animation_group.addAnimation(width_anim)
        animation_group.addAnimation(opacity_anim)
        self.right_panel_animation = animation_group
        animation_group.start()

    def hide_right_panel(self):
        """Hide right panel with animation."""
        if self.right_panel_animation:
            self.right_panel_animation.stop()

        # Store current width for later
        self.stored_right_size = self.right_panel.width()

        animation_group = QParallelAnimationGroup(self)

        # Width animation
        width_anim = QPropertyAnimation(self.right_panel, b"minimumWidth")
        width_anim.setDuration(250)
        width_anim.setStartValue(self.right_panel.width())
        width_anim.setEndValue(0)
        width_anim.setEasingCurve(QEasingCurve.Type.OutCubic)

        # Opacity animation
        opacity_anim = QPropertyAnimation(self.right_panel_opacity, b"opacity")
        opacity_anim.setDuration(250)
        opacity_anim.setStartValue(1.0)
        opacity_anim.setEndValue(0.0)
        opacity_anim.setEasingCurve(QEasingCurve.Type.OutCubic)

        animation_group.addAnimation(width_anim)
        animation_group.addAnimation(opacity_anim)
        animation_group.finished.connect(self.right_panel.hide)

        self.right_panel_animation = animation_group
        animation_group.start()

    def toggle_right_panel(self):
        """Toggle the right panel visibility."""
        if self.right_panel.isVisible():
            self.hide_right_panel()
        else:
            self.show_right_panel()

    def set_left_panel(self, widget):
        """Set the content of the left panel.

        Args:
            widget: Widget to set as the left panel content
        """
        # Clear existing widgets
        while self.left_layout.count():
            item = self.left_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()

        # Add new widget
        self.left_layout.addWidget(widget)

    def set_left_panel_content(self, widget):
        """Set the content of the left panel."""
        self.clear_left_panel()
        if widget:
            self.left_layout.addWidget(widget)
            widget.adjustSize()
            new_width = widget.sizeHint().width() + 20  # Reduced from 40 to account for new 6+6=12px margins + buffer
            self.stored_left_size = new_width
            widget.show()

    def clear_left_panel(self):
        """Clear all widgets from the left panel."""
        while self.left_layout.count():
            item = self.left_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()

    def set_right_panel_content(self, widget):
        """Set the content of the right panel."""
        self.clear_right_panel()
        if widget:
            self.right_layout.addWidget(widget)
            widget.adjustSize()
            new_width = widget.sizeHint().width() + 20  # Reduced from 40 to account for new 6+6=12px margins + buffer
            self.stored_right_size = new_width
            widget.show()

    def clear_right_panel(self):
        """Clear all widgets from the right panel."""
        while self.right_layout.count():
            item = self.right_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()

    def set_center_panel_content(self, widget):
        """Set the content of the center panel.

        Args:
            widget: Widget to set as the center panel content
        """
        # If we have the container setup (with info bar), add to container
        if hasattr(self, 'center_container_layout'):
            # Clear existing module content (keep info bar)
            while self.center_container_layout.count() > 1:  # Keep info bar (last item)
                item = self.center_container_layout.takeAt(0)
                if item.widget():
                    item.widget().deleteLater()

            # Add new widget at the top (before info bar)
            if widget:
                self.center_container_layout.insertWidget(0, widget)
                widget.show()
        else:
            # Fallback to original behavior if container not set up yet
            # Clear existing widgets
            while self.center_layout.count():
                item = self.center_layout.takeAt(0)
                if item.widget():
                    item.widget().deleteLater()

            # Add new widget
            if widget:
                self.center_layout.addWidget(widget)
                widget.show()

    def clear_center_panel(self):
        """Clear all widgets from the center panel."""
        # If we have the container setup (with info bar), clear only module content
        if hasattr(self, 'center_container_layout'):
            # Clear module content (keep info bar)
            while self.center_container_layout.count() > 1:  # Keep info bar (last item)
                item = self.center_container_layout.takeAt(0)
                if item.widget():
                    item.widget().deleteLater()
        else:
            # Fallback to original behavior
            while self.center_layout.count():
                item = self.center_layout.takeAt(0)
                if item.widget():
                    item.widget().deleteLater()

    # ---------- Splash Screen Methods ----------
    def show_splash_content(self, splash_image_path: str):
        """Show splash screen content in the center panel.

        Args:
            splash_image_path: Path to the splash screen image
        """
        # Create splash widget
        self.splash_widget = QWidget()
        self.splash_widget.setObjectName("splash_widget")

        # Create layout for splash content
        splash_layout = QVBoxLayout(self.splash_widget)
        splash_layout.setAlignment(Qt.AlignCenter)
        splash_layout.setContentsMargins(50, 50, 50, 50)

        # Load and display splash image
        splash_pixmap = QPixmap(splash_image_path)
        if not splash_pixmap.isNull():
            # Scale image to reasonable size while maintaining aspect ratio
            splash_pixmap = splash_pixmap.scaled(400, 300, Qt.KeepAspectRatio, Qt.SmoothTransformation)

            # Create image label
            self.splash_image_label = QLabel()
            self.splash_image_label.setPixmap(splash_pixmap)
            self.splash_image_label.setAlignment(Qt.AlignCenter)
            splash_layout.addWidget(self.splash_image_label)

        # Create message label
        self.splash_message_label = QLabel("Loading...")
        self.splash_message_label.setAlignment(Qt.AlignCenter)
        self.splash_message_label.setStyleSheet("""
            QLabel {
                color: #CCCCCC;
                font-size: 14px;
                font-weight: bold;
                margin-top: 20px;
            }
        """)
        splash_layout.addWidget(self.splash_message_label)

        # Set splash content in center panel
        self.set_center_panel_content(self.splash_widget)

    def update_splash_message(self, message: str):
        """Update the splash screen message.

        Args:
            message: New message to display
        """
        if hasattr(self, 'splash_message_label'):
            self.splash_message_label.setText(message)
            # Process events to update UI immediately
            from PySide6.QtWidgets import QApplication
            QApplication.processEvents()

    def hide_splash_content(self):
        """Hide the splash screen content."""
        if hasattr(self, 'splash_widget'):
            self.splash_widget.deleteLater()
            delattr(self, 'splash_widget')
        if hasattr(self, 'splash_image_label'):
            delattr(self, 'splash_image_label')
        if hasattr(self, 'splash_message_label'):
            delattr(self, 'splash_message_label')

    def clear_all_panels(self):
        """Clear all widgets from all panels."""
        self.clear_left_panel()
        self.clear_center_panel()
        self.clear_right_panel()

    def _update_font_size(self):
        """Update font size from config."""
        font = self.font()
        font.setPointSize(gui_config.get_value(GuiKeys.Theme.FONT_SIZE, default=14))
        self.setFont(font)

    def _create_animations(self):
        """Create panel animations."""
        # Create opacity effect for panels
        self.left_panel_opacity = QGraphicsOpacityEffect(self)
        self.right_panel_opacity = QGraphicsOpacityEffect(self)

        # Create width animations
        self.left_panel_animation = QPropertyAnimation(self.left_panel, b"minimumWidth")
        self.right_panel_animation = QPropertyAnimation(
            self.right_panel, b"minimumWidth"
        )

        # Set animation properties
        for animation in [self.left_panel_animation, self.right_panel_animation]:
            animation.setDuration(300)
            animation.setEasingCurve(QEasingCurve.Type.InOutQuad)

    def _save_panel_sizes(self):
        """Save current panel sizes to config."""
        min_width = gui_config.get_value("gui.window.min_panel_width", default=240)
        if self.left_panel and self.left_panel.isVisible():
            width = max(min_width, self.left_panel.width())
            gui_config.set_value(GuiKeys.Panel.LEFT_LAST_WIDTH, width)
        if self.right_panel and self.right_panel.isVisible():
            width = max(min_width, self.right_panel.width())
            gui_config.set_value(GuiKeys.Panel.RIGHT_LAST_WIDTH, width)
        # todo: this method of setting prefferences needs to be documented in fm/docs/architecture/config_system.md

    def _on_font_size_changed(self, size_name):
        """Handle font size changes."""
        size_key = size_name.lower().replace(" ", "_")
        gui_config.set_value(GuiKeys.Theme.FONT_SIZE, size_key)
        # Reload stylesheet with new font size
        style_path = (
            Path(__file__).parent.parent.parent.parent
            / "configs"
            / "stylesheets"
            / "main_stylesheet.qss"
        )
        with open(style_path, encoding="utf-8") as f:
            current_font_size = gui_config.get_value(GuiKeys.Theme.FONT_SIZE)
            style = f.read() % (current_font_size, current_font_size, current_font_size)
            self.setStyleSheet(style)
        # todo: does this work? Doesn't it require an app restart? (implement and test- not a priority)

    def show_right_side_bar(self):
        """Show the right side bar with animation."""
        self.right_side_bar.show()

    def hide_right_side_bar(self):
        """Hide the right side bar with animation."""
        self.right_side_bar.hide()

    def resizeEvent(self, event):
        """Handle window resize event."""
        # Call parent's resizeEvent first
        super().resizeEvent(event)
        
        # Update the splitter sizes if the window is resized
        if hasattr(self, 'splitter') and self.splitter:
            available_width = self.content_container.width()
            right_side_bar_width = self.right_side_bar.sizeHint().width()
            
            sizes = self.splitter.sizes()
            if len(sizes) >= 4:  # Make sure we have all panels
                left_size = sizes[0]
                right_size = sizes[2]
                center_size = available_width - (left_size + right_size + right_side_bar_width)
                
                # Ensure minimum sizes are respected
                if center_size < 100:  # Minimum center size
                    center_size = 100
                    left_size = (available_width - right_side_bar_width - center_size - right_size) // 2
                    right_size = available_width - right_side_bar_width - center_size - left_size
                
                self.splitter.setSizes([left_size, center_size, right_size, right_side_bar_width])

    def closeEvent(self, event):
        """Handle window close event."""
        # Save current panel sizes
        self._save_panel_sizes()
        # Save window size
        gui_config.set_window_size(self.width(), self.height())
        event.accept()
