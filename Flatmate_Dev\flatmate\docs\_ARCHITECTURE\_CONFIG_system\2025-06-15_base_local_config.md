# BaseLocalConfig Documentation

## Overview

`BaseLocalConfig` is an abstract base class that provides local configuration management for components in the Flatmate application. It serves as a bridge between component-specific configuration needs and the global configuration system.

## Location

- **Current:** `fm/core/config/base_local_config.py`
- **Previously:** `fm/modules/base/config/base_module_config.py` (renamed & moved in 2025-06-15)

## Purpose

BaseLocalConfig provides:

1. **Component-specific config access** with type-safe key management
2. **Configuration hierarchy** handling (hardcoded → component YAML → user preferences)
3. **Integration with global config system** while maintaining component isolation
4. **Event bus access** for configuration change notifications
5. **YAML-based defaults** loading from component directories

## Architecture

```
Global ConfigManager (singleton) ← BaseLocalConfig (abstraction) ← Component Configs
```

The abstraction layer allows components to have their own configuration logic without directly manipulating the global singleton config system.

## Usage

### Creating a Component Config

1. **Define component keys:**
```python
# my_component/config/my_keys.py
from enum import Enum

class MyComponentKeys:
    class Settings(str, Enum):
        ENABLED = 'my_component.enabled'
        MAX_ITEMS = 'my_component.max_items'
        
    class Paths(str, Enum):
        DATA_DIR = 'my_component.paths.data'
        CACHE_DIR = 'my_component.paths.cache'
```

2. **Create config class:**
```python
# my_component/config/my_config.py
from ...core.config.base_local_config import BaseLocalConfig
from .my_keys import MyComponentKeys

class MyComponentConfig(BaseLocalConfig[MyComponentKeys]):
    """Configuration manager for MyComponent."""
    
    def get_defaults_file_path(self) -> Path:
        """REQUIRED: Specify path to component's defaults.yaml file."""
        return Path(__file__).parent / "defaults.yaml"
    
    def get_defaults(self) -> dict:
        """Provide hardcoded defaults."""
        return {
            MyComponentKeys.Settings.ENABLED: True,
            MyComponentKeys.Settings.MAX_ITEMS: 100,
            MyComponentKeys.Paths.DATA_DIR: '~/.flatmate/data/my_component',
            MyComponentKeys.Paths.CACHE_DIR: '~/.flatmate/cache/my_component'
        }
```

3. **Use in component:**
```python
# my_component/my_component.py
from .config.my_config import MyComponentConfig, MyComponentKeys

class MyComponent:
    def __init__(self):
        self.config = MyComponentConfig()
        
    def is_enabled(self) -> bool:
        return self.config.get_value(MyComponentKeys.Settings.ENABLED)
        
    def get_max_items(self) -> int:
        return self.config.get_value(MyComponentKeys.Settings.MAX_ITEMS, default=50)
```

### Configuration Hierarchy

BaseLocalConfig implements a clear configuration hierarchy:

1. **User Preferences** (highest priority)
   - File: `~/.flatmate/preferences.yaml`
   - User-customized settings that override everything else

2. **Component Defaults** (middle priority)
   - File: `component/config/defaults.yaml` (optional)
   - Component-specific defaults from YAML file

3. **Hardcoded Defaults** (lowest priority)
   - Method: `get_defaults()` implementation
   - Fallback values defined in code

### Available Methods

```python
# Get configuration values
value = config.get_value(key, default=None)
path = config.get_path(key)  # Returns string path

# Set configuration values
config.set_value(key, value)

# User preferences (higher-level config keys)
pref = config.get_pref(key, default=None)
config.set_pref(key, value)

# Event bus access
config.events.emit(Events.CONFIG_CHANGED, {'key': key, 'value': value})
```

## Current Implementations

### GUI Configuration
```python
# fm/gui/config/gui_config.py
class GuiConfig(BaseLocalConfig[GuiKeyType]):
    """Configuration for GUI settings like window size, themes, etc."""
    
    def get_defaults_file_path(self) -> Path:
        """Get the path to the GUI defaults.yaml file."""
        return Path(__file__).parent / "defaults.yaml"
```

### Update Data Configuration
```python
# fm/modules/update_data/config/ud_config.py
class UpdateDataConfig(BaseLocalConfig[UpdateDataKeys]):
    """Configuration for update data module settings."""
    
    def get_defaults_file_path(self) -> Path:
        """Get the path to the Update Data defaults.yaml file."""
        return Path(__file__).parent / "defaults.yaml"
```

### Home Module Configuration
```python
# fm/modules/home/<USER>/local_config.py
class HomeConfig(BaseLocalConfig[HomeKeys]):
    """Configuration for home module settings."""
    
    def get_defaults_file_path(self) -> Path:
        """Get the path to the Home defaults.yaml file."""
        return Path(__file__).parent / "defaults.yaml"
```

## Design Decisions

### Why BaseLocalConfig Exists

1. **Type Safety:** Components get type-safe access to their specific configuration keys
2. **Isolation:** Components don't directly manipulate the global config singleton
3. **Hierarchy:** Automatic handling of defaults → component YAML → user preferences
4. **Consistency:** Standardized configuration pattern across all components

### Why Not Merge with Core Config

- **Singleton Pattern:** Core config must remain a singleton; changing it breaks everything
- **Different Responsibilities:** Core handles persistence, Local handles component logic
- **Type Safety:** Components need their own key types and validation
- **Config Hierarchy:** Components need local YAML defaults that core config doesn't handle

### The ensure_defaults Pattern

The `ensure_defaults` method represents a significant improvement in configuration management:

1. **Configuration Reflects Actual Usage:** Only configuration items that are actually used by the code will exist in the system. This eliminates "speculative configuration" - settings that developers think might be needed someday but aren't actually used.

2. **Self-Documenting Requirements:** By examining the `ensure_defaults` calls, you can immediately see what configuration a component actually requires to function properly.

3. **Reduced Maintenance Burden:** No need to maintain unused configuration items or wonder if removing something will break functionality.

4. **Clean Evolution:** As features are added or removed, the configuration naturally evolves to match actual usage patterns without manual pruning.

5. **Resilience:** Components can ensure their required configuration exists without depending on external configuration files being perfectly maintained.

With this approach, you could theoretically delete all YAML files and let the system rebuild only what's needed based on the `ensure_defaults` calls throughout the codebase. The configuration would then grow organically based on actual usage rather than speculation.

### Naming Evolution

- **Original:** `BaseModuleConfig` - implied only modules could use it
- **Current:** `BaseLocalConfig` - accurately reflects that any component can use it
- **Reason:** GUI and other non-module components also need configuration management

## Best Practices

### Do's
- ✅ **Inherit from BaseLocalConfig** for any component needing configuration
- ✅ **Use type-safe enum keys** rather than string literals
- ✅ **Provide sensible defaults** in `get_defaults()`
- ✅ **Keep key names descriptive** and properly namespaced
- ✅ **Use the config hierarchy** - let user preferences override defaults

### Don'ts
- ❌ **Don't modify core config directly** - use the abstraction layer
- ❌ **Don't break the singleton pattern** - core config must remain singleton
- ❌ **Don't hardcode paths** - use the path resolution methods
- ❌ **Don't ignore the hierarchy** - respect user preferences over defaults

## Troubleshooting

### Common Issues

1. **Import Errors:**
   ```python
   # Wrong (old path)
   from ....modules.base.config.base_module_config import BaseModuleConfig
   
   # Correct (new path)
   from ....core.config.base_local_config import BaseLocalConfig
   ```

2. **Configuration Not Loading:**
   - Check that `get_defaults_file_path()` is implemented
   - Verify YAML file syntax if using component defaults
   - Ensure proper key naming and enum definitions

3. **Type Errors:**
   - Ensure your keys class is properly typed
   - Use `Union[LocalKeys, str]` for key parameters
   - Check that enum values are strings

### Debug Tips

- Configuration loading is silent except for errors
- Check application logs for "Error processing config key" messages
- Use `config.get_value()` to verify values are set correctly
- Test with minimal defaults first, then add complexity

## Migration Guide

### Updating Existing Code

If you have code using the old `BaseModuleConfig`:

1. **Update imports:**
   ```python
   from ....core.config.base_local_config import BaseLocalConfig
   ```

2. **Update class inheritance:**
   ```python
   class MyConfig(BaseLocalConfig[MyKeys]):  # was BaseModuleConfig
   ```

3. **Update documentation:**
   - Replace "module-specific" with "component-specific"
   - Update any file path references

### Creating New Components

For new components that need configuration:

1. Create a keys file with proper enum structure
2. Create a config class inheriting from BaseLocalConfig
3. Implement `get_defaults_file_path()` with explicit defaults file path
4. Use the config in your component with type-safe key access

## Related Documentation

- [Core Config README](../../src/fm/core/config/README.md) - Overview of entire config system
- [Config Serialization Fix](config_serialization_fix_summary.md) - YAML serialization details
- [Architecture Overview](../z_archive/core_architecture.md) - How config fits into overall architecture

