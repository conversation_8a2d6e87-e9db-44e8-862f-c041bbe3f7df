# Flatmate Application Styling System

## Overview

The Flatmate application uses a structured approach to styling with QSS (Qt Style Sheets). The system is designed to support theming, consistent styling, and potential future enhancements like dynamic font sizing.

## File Structure

The styling system consists of three main QSS files:

1. **palette.qss** - Defines color variables and constants
2. **theme.qss** - Imports palette.qss and applies variables to styling rules
3. **style.qss** - Contains additional styling rules (legacy approach with hardcoded values)

## How Styles Are Applied

The application styling is applied during initialization in `main.py`:

```python
# Create Qt Application
app = QApplication(sys.argv)

# Load and apply styles
from src.fm.gui.styles import apply_styles
apply_styles(app)
```

The `apply_styles` function:
1. Loads and combines the QSS files
2. Applies any dynamic values (like font size from config)
3. Sets the combined stylesheet on the QApplication instance

## Style Loading Process

The style loading process is handled by the `load_styles()` function in `src/fm/gui/styles/__init__.py`:

```python
def load_styles() -> str:
    """Load and combine application styles."""
    styles_dir = Path(__file__).parent
    
    # Load base theme and styles
    with open(styles_dir / "theme.qss", 'r') as f:
        theme = f.read()
    with open(styles_dir / "style.qss", 'r') as f:
        style = f.read()
        
    # Apply any dynamic values
    font_size = config.get_value(ConfigKeys.App.BASE_FONT_SIZE, 14)
    combined = theme + "\n" + style
    combined = combined.replace('{{FONT_SIZE}}', str(font_size))
    
    return combined
```

## QSS File Relationships

### palette.qss

Contains CSS-like variables for colors and other theme constants:

```css
:root {
    /* Primary Colors */
    --color-primary: #3B8A45;
    --color-primary-hover: #4BA357;
    
    /* Text Colors */
    --color-text-primary: #FFFFFF;
    --color-text-secondary: #B0B0B0;
    
    /* Icon Colors */
    --color-icon-normal: #FFFFFF;
}
```

### theme.qss

Imports palette.qss and uses its variables:

```css
/* Import color palette */
@import url("palette.qss");

QWidget {
    background-color: var(--color-bg-dark);
    color: var(--color-text-primary);
}
```

### style.qss

Contains additional styling with hardcoded values:

```css
/* Global styles */
* {
    font-family: ".AppleSystemUIFont", "Helvetica Neue", Arial, sans-serif;
    font-size: 1em;  /* Base font size */
}
```

## SVG Icon Handling

SVG icons with `currentColor` or explicit color values are handled through:

1. **QSS Variables** - For icons that respect CSS inheritance
2. **Custom Rendering** - For icons requiring explicit color control

The `NavButton` class in `nav_pane.py` demonstrates a custom approach to SVG rendering with explicit color control using `QPainter` composition.

## Font Sizing

The application supports configurable font sizing through:

1. A base font size defined in configuration
2. Dynamic replacement of `{{FONT_SIZE}}` placeholder in QSS
3. Hooks for future dynamic font size updates

## Best Practices

1. **Use Variables** - Define colors and other constants in palette.qss
2. **Consistent Naming** - Follow established naming conventions
3. **Component-Specific Styles** - Group styles by component
4. **Avoid Inline Styles** - Keep styling in QSS files when possible

## QSS Limitations and Gotchas

1. **Variable Limitations** - QSS variable support is limited and can be finicky
2. **Selector Specificity** - QSS doesn't always follow CSS specificity rules
3. **Inheritance Issues** - Property inheritance doesn't always work as expected
4. **SVG Color Handling** - SVG color inheritance via `currentColor` is inconsistent
5. **Limited Selectors** - QSS supports only a subset of CSS selectors
6. **No Media Queries** - No support for responsive design via media queries 
==wtf are media queries?==
7. **Debugging Challenges** - Error reporting for QSS is minimal

## Future Enhancements

1. **Dynamic Theming** - Support for runtime theme switching
2. **Improved Font Scaling** - Better support for accessibility
3. **Style Consolidation** - Merge style.qss into theme.qss using variables
