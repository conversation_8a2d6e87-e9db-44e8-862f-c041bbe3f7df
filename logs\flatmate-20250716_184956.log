2025-07-16 18:49:56 - [fm.core.services.master_file_service] [INFO] - MasterFileService initialized
2025-07-16 18:49:57 - [fm.core.services.cache_service] [INFO] - CacheService initialized
2025-07-16 18:49:57 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-07-16 18:49:57 - [CoopStandardCSVHandler] [DEBUG] - Initialized CoopStandardCSVHandler
2025-07-16 18:49:57 - [ASBStandardCSVHandler] [DEBUG] - Initialized ASBStandardCSVHandler
2025-07-16 18:49:57 - [fm.modules.update_data.services.local_services] [DEBUG] - UpdateDataServices initialized
2025-07-16 18:49:57 - [fm.core.config.base_local_config_v2] [DEBUG] - Loaded config hierarchy for categorize: 0 component defaults, 18 user preferences
2025-07-16 18:49:57 - [fm.core.config.base_local_config_v2] [DEBUG] - CategorizeConfig for categorize initialized.
2025-07-16 18:49:57 - [fm.main] [INFO] - Application starting...
2025-07-16 18:50:00 - [fm.main] [INFO] - 
=== Setting up Module Coordinator ===
2025-07-16 18:50:00 - [fm.module_coordinator] [INFO] - Initializing Module Coordinator
2025-07-16 18:50:00 - [fm.module_coordinator] [DEBUG] - Loaded recent modules: ['home', 'categorize', 'update_data']
2025-07-16 18:50:00 - [fm.module_coordinator] [INFO] - Creating all modules (eager loading)
2025-07-16 18:50:00 - [fm.modules.base.base_presenter] [DEBUG] - Initialized HomePresenter
2025-07-16 18:50:00 - [fm.modules.home.home_presenter] [DEBUG] - Home Presenter initialization complete
2025-07-16 18:50:00 - [fm.modules.base.base_presenter] [DEBUG] - Initialized UpdateDataPresenter
2025-07-16 18:50:00 - [fm.modules.base.base_presenter] [DEBUG] - Initialized CategorizePresenter
2025-07-16 18:50:00 - [fm.core.database.sql_repository.sqlite_repository] [INFO] - Using database at: C:\Users\<USER>\.flatmate\data\transactions.db
2025-07-16 18:50:00 - [fm.core.data_services.db_io_service] [DEBUG] - DBIOService initialized with 445MB cache limit
2025-07-16 18:50:00 - [fm.module_coordinator] [INFO] - Setting up home module
2025-07-16 18:50:00 - [fm.modules.base.base_presenter] [INFO] - Setting up HomePresenter
2025-07-16 18:50:00 - [fm.modules.home.home_presenter] [DEBUG] - Connecting Home View signals
2025-07-16 18:50:00 - [fm.modules.base.base_presenter] [DEBUG] - HomePresenter setup complete
2025-07-16 18:50:00 - [fm.module_coordinator] [INFO] - Setting up update_data module
2025-07-16 18:50:00 - [fm.modules.base.base_presenter] [INFO] - Setting up UpdateDataPresenter
2025-07-16 18:50:00 - [fm.modules.update_data.ud_presenter] [DEBUG] - Signals connected
2025-07-16 18:50:00 - [fm.modules.base.base_presenter] [DEBUG] - UpdateDataPresenter setup complete
2025-07-16 18:50:00 - [fm.module_coordinator] [INFO] - Setting up categorize module
2025-07-16 18:50:00 - [fm.modules.base.base_presenter] [INFO] - Setting up CategorizePresenter
2025-07-16 18:50:00 - [fm.core.database.sql_repository.sqlite_repository] [INFO] - Using database at: C:\Users\<USER>\.flatmate\data\transactions.db
2025-07-16 18:50:00 - [fm.core.data_services.db_io_service] [DEBUG] - DBIOService initialized with 441MB cache limit
2025-07-16 18:50:00 - [fm.core.data_services.db_io_service] [WARNING] - Cache miss. Falling back to database query for unique account numbers.
2025-07-16 18:50:01 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Initializing TransactionViewPanel
2025-07-16 18:50:01 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING START: TransactionViewPanel._init_ui
2025-07-16 18:50:01 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Setting up TransactionViewPanel UI
2025-07-16 18:50:01 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Creating CustomTableView_v2 for transactions
2025-07-16 18:50:01 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - TransactionViewPanel UI setup complete
2025-07-16 18:50:01 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING END: TransactionViewPanel._init_ui took 0.026s
2025-07-16 18:50:01 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Connecting TransactionViewPanel signals
2025-07-16 18:50:01 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - TransactionViewPanel signals connected
2025-07-16 18:50:01 - [fm.modules.categorize.cat_presenter] [DEBUG] - About to call _load_data_during_setup()
2025-07-16 18:50:01 - [fm.modules.categorize.cat_presenter] [DEBUG] - Loading data during setup (eager loading)
2025-07-16 18:50:01 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.database.last_account = None (Source: cat_presenter.py:_load_data_during_setup)
2025-07-16 18:50:01 - [fm.modules.categorize.cat_presenter] [INFO] - Auto-loading ALL transactions from database...
2025-07-16 18:50:01 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING START: CategorizePresenter._handle_load_db
2025-07-16 18:50:01 - [fm.modules.categorize.cat_presenter] [INFO] - Loading transactions from database for categorisation…
2025-07-16 18:50:01 - [fm.modules.categorize.cat_presenter] [DEBUG] - Filters: None
2025-07-16 18:50:01 - [fm.modules.categorize.cat_presenter] [DEBUG] - Fetching transactions with filters: {}
2025-07-16 18:50:01 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING START: Data Retrieval from Cache
2025-07-16 18:50:01 - [fm.core.data_services.db_io_service] [WARNING] - Cache miss. Falling back to database query.
2025-07-16 18:50:01 - [fm.core.utils.timing_decorator] [ERROR] - ⏱️  TIMING ERROR: Data Retrieval from Cache failed after 0.002s
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\categorize\cat_presenter.py", line 336, in _handle_load_db
    df = self.data_service.get_transactions_dataframe(**filter_kwargs)
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\data_services\db_io_service.py", line 247, in get_transactions_dataframe
    return self._fetch_raw_transactions_df(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        all_cols=True,
        ^^^^^^^^^^^^^^
        only_columns_with_data=False,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        **filters
        ^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\data_services\db_io_service.py", line 146, in _fetch_raw_transactions_df
    transactions = self.list_transactions(**filters)
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\data_services\db_io_service.py", line 218, in list_transactions
    cached_transactions = self._cache.get_transactions(**filters)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'DBCachingService' object has no attribute 'get_transactions'
2025-07-16 18:50:01 - [fm.modules.categorize.cat_presenter] [ERROR] - Error loading transactions: 'DBCachingService' object has no attribute 'get_transactions'
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\categorize\cat_presenter.py", line 336, in _handle_load_db
    df = self.data_service.get_transactions_dataframe(**filter_kwargs)
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\data_services\db_io_service.py", line 247, in get_transactions_dataframe
    return self._fetch_raw_transactions_df(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        all_cols=True,
        ^^^^^^^^^^^^^^
        only_columns_with_data=False,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        **filters
        ^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\data_services\db_io_service.py", line 146, in _fetch_raw_transactions_df
    transactions = self.list_transactions(**filters)
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\data_services\db_io_service.py", line 218, in list_transactions
    cached_transactions = self._cache.get_transactions(**filters)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'DBCachingService' object has no attribute 'get_transactions'
2025-07-16 18:50:01 - [fm.core.utils.timing_decorator] [ERROR] - ⏱️  TIMING ERROR: CategorizePresenter._handle_load_db failed after 0.041s: 'DBCachingService' object has no attribute 'get_transactions'
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\utils\timing_decorator.py", line 36, in wrapper
    result = func(*args, **kwargs)
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\categorize\cat_presenter.py", line 336, in _handle_load_db
    df = self.data_service.get_transactions_dataframe(**filter_kwargs)
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\data_services\db_io_service.py", line 247, in get_transactions_dataframe
    return self._fetch_raw_transactions_df(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        all_cols=True,
        ^^^^^^^^^^^^^^
        only_columns_with_data=False,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        **filters
        ^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\data_services\db_io_service.py", line 146, in _fetch_raw_transactions_df
    transactions = self.list_transactions(**filters)
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\data_services\db_io_service.py", line 218, in list_transactions
    cached_transactions = self._cache.get_transactions(**filters)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'DBCachingService' object has no attribute 'get_transactions'
2025-07-16 18:50:01 - [fm.modules.categorize.cat_presenter] [ERROR] - Error during _load_data_during_setup(): 'DBCachingService' object has no attribute 'get_transactions'
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\categorize\cat_presenter.py", line 60, in _connect_signals
    self._load_data_during_setup()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\categorize\cat_presenter.py", line 97, in _load_data_during_setup
    self._auto_load_from_database()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\categorize\cat_presenter.py", line 134, in _auto_load_from_database
    self._handle_load_db(filters=None)  # No filters = load everything
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\utils\timing_decorator.py", line 36, in wrapper
    result = func(*args, **kwargs)
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\categorize\cat_presenter.py", line 336, in _handle_load_db
    df = self.data_service.get_transactions_dataframe(**filter_kwargs)
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\data_services\db_io_service.py", line 247, in get_transactions_dataframe
    return self._fetch_raw_transactions_df(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        all_cols=True,
        ^^^^^^^^^^^^^^
        only_columns_with_data=False,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        **filters
        ^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\data_services\db_io_service.py", line 146, in _fetch_raw_transactions_df
    transactions = self.list_transactions(**filters)
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\data_services\db_io_service.py", line 218, in list_transactions
    cached_transactions = self._cache.get_transactions(**filters)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'DBCachingService' object has no attribute 'get_transactions'
2025-07-16 18:50:01 - [fm.modules.categorize.cat_presenter] [ERROR] - Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\categorize\cat_presenter.py", line 60, in _connect_signals
    self._load_data_during_setup()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\categorize\cat_presenter.py", line 97, in _load_data_during_setup
    self._auto_load_from_database()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\categorize\cat_presenter.py", line 134, in _auto_load_from_database
    self._handle_load_db(filters=None)  # No filters = load everything
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\utils\timing_decorator.py", line 36, in wrapper
    result = func(*args, **kwargs)
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\categorize\cat_presenter.py", line 336, in _handle_load_db
    df = self.data_service.get_transactions_dataframe(**filter_kwargs)
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\data_services\db_io_service.py", line 247, in get_transactions_dataframe
    return self._fetch_raw_transactions_df(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        all_cols=True,
        ^^^^^^^^^^^^^^
        only_columns_with_data=False,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        **filters
        ^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\data_services\db_io_service.py", line 146, in _fetch_raw_transactions_df
    transactions = self.list_transactions(**filters)
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\data_services\db_io_service.py", line 218, in list_transactions
    cached_transactions = self._cache.get_transactions(**filters)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'DBCachingService' object has no attribute 'get_transactions'
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\categorize\cat_presenter.py", line 60, in _connect_signals
    self._load_data_during_setup()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\categorize\cat_presenter.py", line 97, in _load_data_during_setup
    self._auto_load_from_database()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\categorize\cat_presenter.py", line 134, in _auto_load_from_database
    self._handle_load_db(filters=None)  # No filters = load everything
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\utils\timing_decorator.py", line 36, in wrapper
    result = func(*args, **kwargs)
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\categorize\cat_presenter.py", line 336, in _handle_load_db
    df = self.data_service.get_transactions_dataframe(**filter_kwargs)
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\data_services\db_io_service.py", line 247, in get_transactions_dataframe
    return self._fetch_raw_transactions_df(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        all_cols=True,
        ^^^^^^^^^^^^^^
        only_columns_with_data=False,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        **filters
        ^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\data_services\db_io_service.py", line 146, in _fetch_raw_transactions_df
    transactions = self.list_transactions(**filters)
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\data_services\db_io_service.py", line 218, in list_transactions
    cached_transactions = self._cache.get_transactions(**filters)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'DBCachingService' object has no attribute 'get_transactions'
2025-07-16 18:50:01 - [fm.modules.base.base_presenter] [DEBUG] - CategorizePresenter setup complete
2025-07-16 18:50:01 - [fm.module_coordinator] [INFO] - All modules created and configured
2025-07-16 18:50:01 - [fm.module_coordinator] [DEBUG] - Available modules: ['home', 'update_data', 'categorize']
2025-07-16 18:50:01 - [fm.main] [INFO] - 
=== Initializing Database Cache ===
2025-07-16 18:50:01 - [fm.core.database.sql_repository.sqlite_repository] [INFO] - Using database at: C:\Users\<USER>\.flatmate\data\transactions.db
2025-07-16 18:50:01 - [fm.core.data_services.db_io_service] [DEBUG] - DBIOService initialized with 437MB cache limit
2025-07-16 18:50:01 - [fm.core.data_services.cache.db_caching] [INFO] - Initializing database cache...
2025-07-16 18:50:01 - [fm.core.data_services.cache.db_caching] [INFO] - Loading all transactions from database with all columns...
2025-07-16 18:50:01 - [fm.core.data_services.cache.db_caching] [ERROR] - Failed to initialize database cache: 'DBCachingService' object has no attribute 'get_transactions'
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\data_services\cache\db_caching.py", line 76, in initialize_cache
    self._cached_df = db_io_service._fetch_raw_transactions_df(
                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        all_cols=True,  # Load ALL columns, not just display columns
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        only_columns_with_data=False  # Include all columns even if empty
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\data_services\db_io_service.py", line 146, in _fetch_raw_transactions_df
    transactions = self.list_transactions(**filters)
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\data_services\db_io_service.py", line 218, in list_transactions
    cached_transactions = self._cache.get_transactions(**filters)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'DBCachingService' object has no attribute 'get_transactions'
2025-07-16 18:50:01 - [fm.main] [WARNING] - Database cache initialization failed - modules will query database directly
2025-07-16 18:50:01 - [fm.module_coordinator] [INFO] - Starting Application
2025-07-16 18:50:01 - [fm.module_coordinator] [INFO] - Transitioning from None to home
2025-07-16 18:50:01 - [fm.module_coordinator] [DEBUG] - Showing home module
2025-07-16 18:50:01 - [fm.modules.base.base_presenter] [INFO] - Showing HomePresenter
2025-07-16 18:50:01 - [fm.modules.base.base_module_view] [INFO] - Setting up HomeView in Main Window
2025-07-16 18:50:01 - [fm.modules.base.base_module_view] [DEBUG] - Setting up Left Panel
2025-07-16 18:50:01 - [fm.modules.base.base_module_view] [DEBUG] - Setting up Center Panel
2025-07-16 18:50:01 - [fm.modules.base.base_module_view] [INFO] - HomeView setup complete
2025-07-16 18:50:01 - [fm.modules.home.home_presenter] [DEBUG] - Refreshing Home content
2025-07-16 18:50:01 - [fm.modules.home.home_presenter] [DEBUG] - Home content refresh complete
2025-07-16 18:50:01 - [fm.modules.base.base_presenter] [DEBUG] - HomePresenter is now visible
2025-07-16 18:50:01 - [fm.module_coordinator] [INFO] - Successfully transitioned to home
2025-07-16 18:50:01 - [fm.main] [INFO] - 
=== Application Ready ===
