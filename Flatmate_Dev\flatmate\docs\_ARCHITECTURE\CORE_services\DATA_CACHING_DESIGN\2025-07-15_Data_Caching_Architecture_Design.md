# Data Caching Architecture Design Document

*Date: 2025-07-15*
*Purpose: Design app-wide data caching strategy for transaction data*

## 🎯 **Core Objective**

Move from database-query-per-request to **memory-centric architecture** where:
- Entire transaction database loads into memory at app startup
- All filtering/querying happens in-memory for instant results
- Database is only hit for writes/updates, not reads

## 🏗️ **Architectural Considerations**

### **Use Cases to Support**

#### **Primary Use Cases**
1. **Categorize Module**: Filter transactions by date/account, instant results
2. **Reports Module** (future): Aggregate data across date ranges, accounts
3. **Search/Filter**: Real-time search across transaction details
4. **Dashboard Views**: Quick stats, recent transactions, summaries

#### **Secondary Use Cases**
1. **Data Import**: New transactions added to both memory and database
2. **Transaction Updates**: Category/tag changes reflected in memory
3. **Account Management**: Account changes update cached data
4. **Backup/Export**: Memory cache can be source for exports

### **Memory Usage Analysis**

#### **Current Database Size**
- **Test Data**: ~2099 transactions
- **Memory Usage**: 0.0MB (essentially negligible)
- **Production Estimate**: Even 100k transactions likely < 50MB

#### **Memory Scalability**
- **Modern Applications**: Browser tabs use 100-500MB routinely
- **Financial Data**: Transaction records are small (date, amount, description, account)
- **Conclusion**: Memory usage is "vanishingly little" as user noted

## 🔧 **Revised Architecture: DBIOService + Dedicated Cache**

### **Core Principle: Don't Replace, Enhance**
- **DBIOService remains the front-facing API** - all modules continue using it
- **Add dedicated caching layer** that DBIOService employs internally
- **Transparent caching** - modules don't need to change their code

### **Proposed Architecture**
```
DBIOService (existing, enhanced)
├── TransactionCacheService (new internal service)
│   ├── _master_dataframe: DataFrame
│   ├── _memory_limit: int (default 512MB)
│   ├── _cache_enabled: bool
│   └── methods: load_data(), get_filtered(), is_loaded()
├── existing methods (unchanged API)
└── enhanced methods use cache when available
```

### **Memory Management Integration**
```
EnvironmentService (new)
├── get_available_ram() -> int
├── get_recommended_cache_limit() -> int
├── assess_system_resources() -> Dict
└── get_cache_settings() -> Dict
```

### **Data Flow**
```
main.py startup:
├── Initialize DBIOService
├── DBIOService initializes TransactionCacheService
├── EnvironmentService assesses available RAM
├── Cache loads transactions (up to memory limit)
└── DBIOService methods now use cached data

Module usage (unchanged):
├── categorize_module calls db_service.get_transactions_dataframe()
├── DBIOService checks if cache is loaded
├── If cached: return filtered data from memory (instant)
├── If not cached: fallback to database query (current behavior)
```

## 🎯 **Implementation Details**

### **TransactionCacheService** (Internal to DBIOService)
```python
class TransactionCacheService:
    def __init__(self, memory_limit_mb: int = 512):
        self._master_df: Optional[pd.DataFrame] = None
        self._memory_limit_mb = memory_limit_mb
        self._cache_enabled = True
        self._last_refresh: Optional[datetime] = None

    # Core caching methods
    def load_from_database(self, db_service, show_progress=True) -> bool
    def get_filtered_data(self, **filters) -> pd.DataFrame
    def is_loaded(self) -> bool
    def get_memory_usage_mb(self) -> float

    # Memory management
    def check_memory_limit(self, df: pd.DataFrame) -> bool
    def clear_cache(self) -> None
    def refresh_cache(self, db_service) -> bool
```

### **EnvironmentService** (System Resource Assessment)
```python
class EnvironmentService:
    @staticmethod
    def get_available_ram_mb() -> int
    def get_total_ram_mb() -> int
    def get_recommended_cache_limit_mb() -> int  # Default: min(512MB, 25% of available RAM)
    def assess_cache_feasibility(self, estimated_data_size_mb: int) -> bool
```

### **Enhanced DBIOService Methods**
```python
# Existing methods enhanced with caching
def get_transactions_dataframe(self, use_cache=True, **filters) -> pd.DataFrame:
    if use_cache and self._cache.is_loaded():
        return self._cache.get_filtered_data(**filters)  # Instant!
    else:
        return self._fetch_raw_transactions_df(**filters)  # Fallback to DB

def initialize_cache(self, show_progress=True) -> bool:
    # Called by main.py on startup
    return self._cache.load_from_database(self, show_progress)
```

## 🔄 **Data Flow Architecture**

### **Application Startup**
```
main.py
├── Initialize DBIOService (existing)
├── Call db_service.initialize_cache()
├── EnvironmentService assesses RAM and sets limits
├── TransactionCacheService loads data (up to memory limit)
└── Continue with app initialization (modules unchanged)
```

### **Module Data Access** (No Changes Required)
```
categorize_module
├── Call db_service.get_transactions_dataframe(**filters) (existing API)
├── DBIOService checks cache first, falls back to database
├── Receive instant filtered DataFrame (if cached)
└── Display results (existing code)
```

### **Memory Management**
```
Cache Loading Process:
├── EnvironmentService.get_recommended_cache_limit_mb() -> 512MB default
├── Load transactions from database
├── Check if DataFrame size exceeds limit
├── If too large: disable caching, use database queries
├── If acceptable: cache in memory for instant access
```

## 🚀 **Implementation Strategy**

### **Phase 1: Core Caching Infrastructure**
1. Create `EnvironmentService` for RAM assessment
2. Create `TransactionCacheService` for internal caching
3. Enhance `DBIOService` with cache integration
4. Update main.py to initialize cache on startup

### **Phase 2: Cache Optimization**
1. Add memory limit enforcement
2. Implement cache refresh mechanisms
3. Add cache statistics and monitoring
4. Performance optimizations (indexing, pre-filtering)

### **Phase 3: Advanced Features**
1. Cache invalidation on data updates
2. Partial caching for large datasets
3. Configuration options for cache behavior
4. Cache persistence across app restarts (optional)

## 🤔 **Open Questions for Discussion**

### **Service Location**
- `TransactionCacheService`: `fm.core.data_services.cache.transaction_cache_service`
- `EnvironmentService`: `fm.core.services.environment_service`
- Both used internally by `DBIOService`, not exposed to modules

### **Memory Limits**
- **Default**: 512MB cache limit (as you suggested)
- **Dynamic**: 25% of available RAM (whichever is smaller)
- **Configurable**: Allow users to adjust via config if needed

### **Cache Invalidation**
- How do we handle external database changes?
- Should we periodically refresh from database?
- File system watching for database changes?

### **Error Handling**
- What happens if memory cache becomes corrupted?
- Fallback to database queries?
- Automatic cache rebuild?

### **Configuration**
- Should caching be configurable (on/off)?
- Memory usage limits?
- Refresh intervals?

## 📋 **Next Steps**

1. **Discuss and decide** on architecture approach
2. **Define service interface** and method signatures
3. **Choose implementation location** in codebase
4. **Plan integration strategy** with existing modules
5. **Implement Phase 1** with basic functionality

---

**Ready for discussion and refinement based on your requirements and preferences.**
