# Example State Module for Module Initialization

class BaseModuleState:
    """
    Abstract base class for module state determination.
    Provides a template for creating module-specific state modules.
    """
    def __init__(self, config_manager, global_state_tracker):
        """
        Initialize state module with access to global configuration.
        
        Args:
            config_manager: Application-wide configuration manager
            global_state_tracker: Tracks overall application state
        """
        self._config_manager = config_manager
        self._global_state_tracker = global_state_tracker
    
    def get_initial_view_config(self):
        """
        Determine initial view configuration.
        
        Returns:
            dict: Minimal configuration for view initialization
        """
        raise NotImplementedError("Subclasses must implement this method")


class UpdateDataState(BaseModuleState):
    """
    State module specifically for Update Data module.
    Determines initial state based on multiple configuration sources.
    """
    def get_initial_view_config(self):
        """
        Determine view configuration for Update Data module.
        
        Returns:
            dict: Configuration for view initialization
        """
        return {
            'process_button_enabled': self._determine_process_button_state(),
            'has_master_file': self._check_master_file_existence(),
            'mode': self._determine_module_mode()
        }
    
    def _determine_process_button_state(self):
        """
        Decide process button state based on application rules.
        
        Returns:
            bool: Whether process button should be enabled
        """
        # Example complex logic:
        # 1. Check if required configurations are present
        # 2. Validate system readiness
        # 3. Consult global state
        
        # Default to disabled
        return False
    
    def _check_master_file_existence(self):
        """
        Check if master file exists.
        
        Returns:
            bool: Master file existence status
        """
        return self._config_manager.master_exists()
    
    def _determine_module_mode(self):
        """
        Determine initial operational mode for the module.
        
        Returns:
            str: Module's initial mode
        """
        if self._check_master_file_existence():
            return 'view_only'
        return 'default'


# Example usage in presenter
class UpdateDataPresenter:
    def __init__(self, state_module, view_factory):
        """
        Initialize presenter with state module and view factory.
        
        Args:
            state_module (UpdateDataState): Module state determiner
            view_factory: Factory for creating views
        """
        self._state_module = state_module
        self._view_factory = view_factory
        
        # Get initial configuration
        initial_config = self._state_module.get_initial_view_config()
        
        # Create view with initial configuration
        self.view = self._view_factory.create(initial_config)
