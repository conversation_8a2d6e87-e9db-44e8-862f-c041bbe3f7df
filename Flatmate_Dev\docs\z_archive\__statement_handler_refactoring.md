# DESIGN PRINCIPLES:

Ideally, the statement handlers are relatively passive. They describe the statements they are meant to handle.

The base class should handle the bulk of the work, with the handlers only providing specific details for their respective formats.

The individual statement handlers inherit from the base class.
Using the information provided by the statement handlers, the base class has the logic that executes the actual matching and formatting.

This follows DRY principles.

Occasionally it may be necessary to override base class methods for particularly tricky formats.

# AI's ! familiarise yourslelw with the statement handlers and the base class, and  _handler_registry.py
    -[link](flatmate\src\fm\modules\update_data\utils\statement_handlers)
    -[link](flatmate\src\fm\modules\update_data\utils\statement_handlers\_base_statement_handler.py)
    -[link](flatmate\src\fm\modules\update_data\utils\statement_handlers\_handler_registry.py)

# CURRENT CONCERNS:

We have created a test to run on the statement handlers and a current working through them. First off the block is the KiwiBank BASIC  CSV handler.

    *Insert link: flatmate\src\fm\modules\update_data\utils\statement_handlers\kiwibank_basic_csv_handler.py

Initial tests show the statement handler is correctly identifying which statements are not its matched type.

However, it's missing its own type, because it is expecting a date in the first column, first row.
First column, first row, A1 ot iloc 0,0 contains the bank account number.

If we are using pd.read_csv() it will be the header - first column
if we are using pd.read_csv() with header=None it will be the first row

## Question 1 : how should we read the csv?
-discuss:

    ### AI observations:
    1. Current issue: `KiwibankBasicCSVHandler` lacks a `can_handle_file` class method, causing it to fail matching.
    2. The handler registry expects `can_handle_file` to validate file format.
    3. For Kiwibank CSV:
    - First row contains account number (not headers)
    - First validation should check account number in row 0
    - Data starts from second row
    - Should use `pd.read_csv(filepath, header=None, skiprows=1)` for data
    4. Suggestion: Implement `can_handle_file` to verify account number pattern in first row.

    ### NOTES: 
    after discussion with ai:
    - Base handler should implement `can_handle_file` using handler attributes
    - Handlers define structure (headers, account number location, etc.)
    - Base class handles validation logic consistently
    - Existing `has_headers` can be used
    - Consider adding `read_headless` for formats like Kiwibank
    - Keeps handler code minimal and focused on format description

    Q: what is the utility of a read_headless attribute?
        -if has_headers = false, wouldnt i follw that read headless = true?

    AI Response: 
        - Correct, `read_headless` is redundant. We can handle this with:
        1. `has_headers = False` (first row is data)
        2. `skip_rows = 1` (skip account number row)
        3. `header = None` in `pd.read_csv`
        - No need for an additional attribute

### Decision Point:
implement can_handle_file method in base handler, create plan and execute

### PLAN: create implementaiton plan for can_handle_file method in base handler
after creatign a sketch .md we figuered out it already has a "can_handle" method.

after modifying that to pd.read with or without headers based on has_headers attribute, 
we got the logging working for the basic kiwibank csv handler.and copied that to other methods 
(involved superinit rather than post init)
we made a change to make the base class method more configurable by the caller. (kwargs)
and made the filename attribute less strict. ( not an early return/ fail) 

I then deleted or commented out all of the bespoke can_handle_file methods from the handlers
we are aiming to get as many working from the base method.
Ony overiding when neccesary.


# Test Results Summary

## Success:
- ✅ **KiwibankBasicCSVHandler**: 2/2 files (49 rows each)
- ✅ **CoopStandardCSVHandler**: 2/2 files (67 rows each)

## Issues:
- ❌ **KiwibankFullCSVHandler**: Account number pattern check failed
- ❌ **AsbStandardCSVHandler**: Header check failed (all 'nan' values)

## Pandas Warnings:
- **Issue**: `SettingWithCopyWarning` in `_base_statement_handler.py` (lines 683, 687, 610)
- **Cause**: Direct DataFrame modifications without `.loc`
- **Fix**: Use `.loc` for DataFrame assignments

# # ACTION TAKEN 

- k bank has headers but it didnt have the attribute.
we fixed that but thene it was looking for the account number in the wrong place.
(weirdly) 
anyway we set that location to (0,0) and it worked ..

so now we seem to have 3 out of 4.
The last is the problem child 
# # ASB Bank Handler Fixes

## Issues Fixed:
1. **Header Row Index**
   - Fixed off-by-one error in header row index (changed from 7 to 6 to match 0-based indexing)
   - Set `has_headers=False` since we're reading the file without headers and handling column names manually

2. **Account Number Extraction**
   - Updated regex pattern to be more permissive while still being specific enough
   - Improved error logging to help diagnose extraction failures
   - Ensured account number is properly extracted from metadata

3. **Column Handling**
   - Configured handler to read file without headers and manually set column names
   - Set up proper column mapping for ASB's format
   - Added handling for the blank line after headers

4. **Base Class Improvements**
   - Simplified column name handling logic
   - Added validation for required fields in `ColumnAttributes`
   - Improved error messages for debugging

## Current Status
- All 4 test files are now processed successfully (100% pass rate)
- Each handler correctly identifies and processes its target format
- Account numbers are properly extracted
- Column mappings are consistent with each bank's format

## Key Learnings
1. **CSV Reading Strategy**: For files with metadata rows, it's often better to read without headers and handle column naming manually
2. **Pattern Matching**: Balance between being specific enough to avoid false positives but permissive enough to handle variations
3. **Error Handling**: Detailed error messages are crucial for diagnosing issues in file parsing
4. **Base Class Design**: The current design with configurable attributes in handlers and common logic in the base class is working well

## Next Steps
1. Monitor for any edge cases in production
2. Consider adding more validation for file formats
3. Document the expected format for each handler in its docstring
