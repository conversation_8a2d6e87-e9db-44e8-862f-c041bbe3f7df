2025-07-17 05:34:59 - [fm.core.services.master_file_service] [INFO] - MasterFileService initialized
2025-07-17 05:35:01 - [fm.core.services.cache_service] [INFO] - CacheService initialized
2025-07-17 05:35:01 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-07-17 05:35:01 - [CoopStandardCSVHandler] [DEBUG] - Initialized CoopStandardCSVHandler
2025-07-17 05:35:01 - [ASBStandardCSVHandler] [DEBUG] - Initialized ASBStandardCSVHandler
2025-07-17 05:35:01 - [fm.modules.update_data.services.local_services] [DEBUG] - UpdateDataServices initialized
2025-07-17 05:35:01 - [fm.core.config.base_local_config_v2] [DEBUG] - Loaded config hierarchy for categorize: 0 component defaults, 18 user preferences
2025-07-17 05:35:01 - [fm.core.config.base_local_config_v2] [DEBUG] - CategorizeConfig for categorize initialized.
2025-07-17 05:35:01 - [fm.main] [INFO] - Application starting...
2025-07-17 05:35:03 - [fm.main] [INFO] - 
=== Setting up Module Coordinator ===
2025-07-17 05:35:03 - [fm.module_coordinator] [INFO] - Initializing Module Coordinator
2025-07-17 05:35:03 - [fm.module_coordinator] [DEBUG] - Loaded recent modules: ['home', 'categorize', 'update_data']
2025-07-17 05:35:03 - [fm.module_coordinator] [INFO] - Creating all modules (eager loading)
2025-07-17 05:35:03 - [fm.modules.base.base_presenter] [DEBUG] - Initialized HomePresenter
2025-07-17 05:35:03 - [fm.modules.home.home_presenter] [DEBUG] - Home Presenter initialization complete
2025-07-17 05:35:03 - [fm.modules.base.base_presenter] [DEBUG] - Initialized UpdateDataPresenter
2025-07-17 05:35:03 - [fm.modules.base.base_presenter] [DEBUG] - Initialized CategorizePresenter
2025-07-17 05:35:03 - [fm.core.data_services.db_io_service] [INFO] - Initializing DBIOService singleton...
2025-07-17 05:35:03 - [fm.core.database.sql_repository.sqlite_repository] [INFO] - Using database at: C:\Users\<USER>\.flatmate\data\transactions.db
2025-07-17 05:35:03 - [fm.core.database.sql_repository.cached_sqlite_repository] [DEBUG] - CachedSQLiteRepository initialized
2025-07-17 05:35:03 - [fm.core.database.sql_repository.cached_sqlite_repository] [INFO] - Warming transaction cache...
