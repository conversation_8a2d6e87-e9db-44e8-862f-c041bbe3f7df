# Categorize Module Performance Enhancement Strategy

**Date:** 15 July 2025

**Author:** Cascade

**Sources:**
- `categorize_module_performance_analysis.md`
- `performance_optimization.md`
- `infobar_integration_handover.md`
- `table_view_loading_performance.md`

---

## 1. Executive Summary & Final Recommendation

The Categorize module currently suffers from significant performance bottlenecks that degrade the user experience, primarily during data loading and filtering. The root causes are an inefficient data-fetching strategy and a slow, blocking UI rendering process.

A multi-faceted approach is required. The `InfoBar` has already been successfully integrated to provide crucial UI feedback, but this does not solve the underlying performance issues.

**Final Recommendation:**

An immediate, two-pronged approach is recommended to deliver the most significant impact with manageable effort:

1.  **Implement a Presenter-Level Data Cache:** Load data once from the database and perform all subsequent filtering and sorting operations on this in-memory cache. This will eliminate redundant database queries and make UI filtering near-instantaneous.
2.  **Utilise Background Worker Threads:** Move the initial, heavy data-loading operation (that populates the cache and table) to a background thread. This will prevent the main UI from freezing and leverage the existing `InfoBar` to show real progress, dramatically improving perceived performance.

For long-term scalability, the `TableView` should be refactored to use **Virtual Scrolling**. This is a more complex task but is the definitive solution to the core rendering bottleneck, ensuring the application remains fast even with extremely large datasets.

---

## 2. The Performance Problem: Analysis & Metrics

Analysis of the provided documents reveals two primary performance issues:

**A. Inefficient Data Loading and Filtering:**
- **Problem:** Every filter change (e.g., selecting a different bank account) triggers a new, full database query and data processing cycle.
- **Impact:** Causes noticeable lag and an unresponsive UI during what should be a simple operation.

**B. Blocking UI and Slow Table Rendering:**
- **Problem:** The `TableView` widget is the single largest bottleneck. It loads and renders the entire dataset in a single, blocking operation on the main thread.
- **Impact:** The application freezes during the initial data load.
- **Metrics:** The issue is quantified in `table_view_loading_performance.md`:
    - **Total Load Time:** **2.8 seconds** for 2,099 transactions.
    - **Bottleneck:** **2.3 seconds (82% of total time)** is spent in the `TableView.set_dataframe()` and configuration methods alone.

---

## 3. Proposed Solutions: A Weighted Analysis

Here is a weighted analysis of the proposed solutions, synthesized from the source documents.

### Approach 1: Data Caching Layer

- **Description:** Introduce a cache (e.g., a pandas DataFrame) within the `CategorizePresenter`. On first load, fetch all relevant transactions from the database into this cache. Subsequent filtering actions will operate on this fast, in-memory cache instead of hitting the database.
- **Pros:**
    - Drastically reduces database load.
    - Makes filtering and sorting operations virtually instantaneous.
    - Relatively simple to implement.
- **Cons:**
    - Increases memory footprint (though likely manageable for typical datasets).
    - The initial data load into the cache can still be slow.
    - Data can become stale, requiring a manual "Refresh" button for users to fetch the latest data.
- **Recommendation:** **High Priority.** This is a foundational improvement that addresses the inefficient filtering strategy with high impact and moderate effort.

### Approach 2: Background Loading (Worker Threads)

- **Description:** Move time-consuming operations, specifically the initial data fetch from the database and the population of the `TableView`, to a background worker thread (e.g., Qt's `QThread`).
- **Pros:**
    - Prevents the UI from freezing, providing a much smoother user experience.
    - Leverages the already-implemented `InfoBar` to provide meaningful, real-time progress updates.
- **Cons:**
    - Does not reduce the *actual* total loading time (2.8 seconds is still 2.8 seconds).
    - Introduces complexities of multi-threading (e.g., ensuring thread safety).
- **Recommendation:** **High Priority.** To be implemented in conjunction with the Data Caching Layer. This combination makes the application *feel* fast and responsive, which is critical for user experience.

### Approach 3: Virtual Scrolling for the Table View

- **Description:** Refactor the `TableView` to only render the rows that are currently visible to the user. As the user scrolls, new rows are rendered on-demand while old ones are destroyed.
- **Pros:**
    - The definitive solution to the table rendering bottleneck.
    - Initial load time becomes almost instant, regardless of dataset size.
    - Minimises memory consumption and ensures smooth scrolling.
- **Cons:**
    - By far the most complex solution to implement, likely requiring a custom data model or a sophisticated proxy model.
- **Recommendation:** **Medium Priority / Long-Term Goal.** This offers the best possible performance but at the highest cost of implementation. It should be scheduled as a follow-up enhancement after the higher-priority, lower-effort solutions have been implemented.

### Approach 4: Incremental UI Optimizations

- **Description:** Applying smaller-scale optimizations to the table, such as pre-calculating column widths, simplifying cell rendering delegates, or batching UI updates.
- **Pros:**
    - Can provide minor performance improvements.
- **Cons:**
    - Unlikely to make a significant dent in the 2.3-second rendering time.
    - These are micro-optimizations that don't address the core architectural problem.
- **Recommendation:** **Low Priority.** These should be considered as part of the larger Virtual Scrolling effort, not as standalone solutions.

---

## 4. Recommended Implementation Roadmap

**Phase 1: Immediate Wins (High Impact / Medium Effort)**

1.  **Implement the Data Cache:** Modify `CategorizePresenter` to hold the master transaction list for the current context. Refactor filtering logic to use this cache.
2.  **Add a "Refresh" Button:** Provide a mechanism for the user to manually reload data from the database.
3.  **Move Initial Load to Worker Thread:** Encapsulate the database fetch and initial `TableView.set_dataframe()` call within a `QThread`. Use the `global_event_bus` to publish progress events to the `InfoBar` and to signal completion.

**Phase 2: Definitive Solution (Highest Impact / High Effort)**

1.  **Architect Virtual Scrolling:** Design and implement a new table model (or proxy model) that supports lazy data loading and pagination.
2.  **Refactor Table View:** Replace the current data loading mechanism with the new virtual scrolling model.
3.  **Profile and Tune:** Measure performance and apply incremental optimizations (like those in Approach 4) as needed.
