# Column System Refactoring Proposal (Revised 2025-07-11)

This document outlines a revised, more powerful implementation of the column management system based on user feedback. It addresses discoverability, type hinting, and the need for columns to belong to multiple groups.

## 1. Core Principles

- **Single Source of Truth**: All column definitions live in one place.
- **Rich, Multi-Group Metadata**: A column can belong to multiple functional groups (e.g., a column can be for `statement_handlers` and also for `ui_display`).
- **Intuitive & Performant API**: A clean, type-hinted `Columns.get('group_name')` method provides easy access to column groups without slow, repetitive loops.
- **Error Prevention**: Using defined objects (`Columns.DATE`) instead of raw strings (`'Date'`) prevents typos.

## 2. Proposed Implementation

### File 1: `column_definition.py` (The Blueprint)

The `Column` dataclass is updated to accept a `list` of groups.

```python
# fm/core/data_services/standards/column_definition.py
from dataclasses import dataclass
from typing import Any, List

@dataclass(frozen=True)
class Column:
    """Represents a single, well-defined column in the application."""
    db_name: str         # The programmatic name (e.g., 'details')
    display_name: str    # The user-facing header (e.g., 'Details')
    dtype: Any
    groups: List[str]

    # Makes the object behave like its display name by default, aligning
    # with the application's convention of using display names internally.
    def __str__(self) -> str:
        return self.display_name
```

### File 2: `columns.py` (The Central Registry)

This is the single source of truth. Each column is defined once with all the groups it belongs to. This avoids the need to combine disparate lists later.

```python
# fm/core/data_services/standards/columns.py
from .column_definition import Column
from datetime import datetime

class Columns:
    """A central registry for all column definitions."""
    # --- Column Definitions ---
    # Note how a column can belong to multiple logical groups.
    DATE    = Column(db_name='date',    display_name='Date',    dtype=datetime, groups=['core', 'statement_handler', 'ui_core'])
    DETAILS = Column(db_name='details', display_name='Details', dtype=str,      groups=['core', 'statement_handler', 'ui_core'])
    AMOUNT  = Column(db_name='amount',  display_name='Amount',  dtype=float,    groups=['core', 'statement_handler', 'ui_core'])
    
    ID      = Column(db_name='id',      display_name='ID',      dtype=int,      groups=['db_system'])
    HASH    = Column(db_name='hash',    display_name='Hash',    dtype=str,      groups=['db_system'])
    
    CATEGORY= Column(db_name='category',display_name='Category',dtype=str,      groups=['user_editable', 'ui_core'])
    # ... all other columns defined here with their groups ...

    # --- Internal cache, built once for performance ---
    _group_map = None

    @classmethod
    def _build_group_map(cls):
        """(Internal) Builds a cache mapping group names to columns."""
        cls._group_map = {}
        for attr in dir(cls):
            member = getattr(cls, attr)
            if isinstance(member, Column):
                for group_name in member.groups:
                    cls._group_map.setdefault(group_name, []).append(member)

    # --- Public API ---
    @classmethod
    def get(cls, group_name: str) -> list[Column]:
        """
        Get a list of all Column objects belonging to a specific group.
        This is fully type-hinted, discoverable, and performant.
        """
        if cls._group_map is None:
            cls._build_group_map()
        return cls._group_map.get(group_name, [])
```

## 3. How to Use It in Code (The Payoff)

This system is simpler and more powerful in practice.

### Example 1: Base Statement Handler

The handler asks for exactly what it needs: the `statement_handler` group. No more importing separate lists.

```python
# _base_statement_handler.py
from fm.core.data_services.standards.columns import Columns

class BaseStatementHandler:
    def _format_df(self, df):
        # ...
        # Get the list of column objects the handler is responsible for.
        expected_cols = Columns.get('statement_handler')
        
        # Filter the DataFrame to only include expected columns that are present.
        # The Column objects behave like their db_name string here.
        final_cols = [str(col) for col in expected_cols if str(col) in df.columns]
        return df[final_cols]
```
**Benefit:** The intent is crystal clear. The handler gets its columns from the single source of truth. There's no ambiguity about which list to use or how it was constructed.

### Example 2: Database Service

The database service can now definitively know which columns are its own and can enforce architectural rules.

```python
# db_io_service.py
from fm.core.data_services.standards.columns import Columns

def update_database(df):
    # Get the display_names of database-managed columns
    system_col_display_names = [c.display_name for c in Columns.get('db_system')]

    # Check if any system columns were improperly passed into the dataframe
    for col_name in df.columns:
        if col_name in system_col_display_names:
            # This is a clear architectural violation
            raise ValueError(f"Input DataFrame must not contain system column: {col_name}")
    
    # ... now, inside this service, you would map display_names to db_names before writing ...
    
    # ... proceed with clean data ...
```
**Benefit:** The logic is simple and robust. The service isn't cleaning up data; it's enforcing a contract. This prevents entire classes of bugs.

### Example 3: Building a UI Table

When you need to build a table view, you just ask for the columns in the `ui_core` group.

```python
# some_ui_module.py
from fm.core.data_services.standards.columns import Columns

def create_main_table_view(data_df):
    # Get only the columns meant for the main UI, in the correct order.
    ui_columns = Columns.get('ui_core')
    
    # Create the view with just those columns
    table_df = data_df[[str(c) for c in ui_columns]]
    # ... build and display the table ...
```
**Benefit:** The UI logic is decoupled from the data source. If you want to add a column to the table, you just add the `'ui_core'` group to its definition in `columns.py`. No other code needs to change.

### Example 4: Referencing a Specific Column Safely (The Enum Use-Case)

This new system fully preserves the safety of using an enum for referencing a specific column. Instead of an enum, you use the central `Columns` registry.

```python
# some_processing_function.py
from fm.core.data_services.standards.columns import Columns
import pandas as pd

def process_transactions(df: pd.DataFrame):
    # Get the canonical, typo-proof reference to the 'date' column object
    date_col = Columns.DATE

    # Access its specific properties safely
    print(f"Programmatic DB name: '{date_col.db_name}'")       # -> 'date'
    print(f"Default string representation: '{str(date_col)}'") # -> 'Date'

    # Use the default string representation (display_name) to safely access the DataFrame column
    if str(date_col) in df.columns:
        df[str(date_col)] = pd.to_datetime(df[str(date_col)])

    return df
```
**Benefit:** This is the best of both worlds. You get the compile-time safety and auto-completion of referencing `Columns.DATE` (just like an enum), but you also get a much richer object that contains all the column's metadata, ready to use.
