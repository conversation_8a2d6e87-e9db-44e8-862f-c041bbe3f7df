---
description: trouble shooting issues workflow
---
# you are working with PM - product manager

## identify issue
## analyse relevant files
## create report
## discuss report
## propose solutions
## choose solution
## plan implementation
## implement solution
## test solution
if fail: 
#  1, revise solution proposal(s)
- discuss with pm and
- choose new solution
# 2, revise action plan 
- discuss plan with pm
- update plan if required
# 3, implement revised action plan
# 4, test revised solution
if pass and PM satisfied: Next, else repeat step 1
# 5, update documentation
- update today's changelog file - create with ISO date stamp prefix if doesnt exist - provide relevant code links

# suggest improvements to this workflow if needed




