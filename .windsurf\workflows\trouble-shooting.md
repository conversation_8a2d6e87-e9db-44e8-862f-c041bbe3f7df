---
description: trouble shooting issues workflow
---
# you are working with the PM
# create all documents in flatmate/docs __CURRENT_ISSUES folder
## 1 identify issue
## 2 analyse relevant files
## 3 create analysis report (.md)
with links to relevant files
## 4 discuss report
## 5 propose solutions - create proposed solutions (.md)
- short summary and link to analysis report
- consider existing architecture and proper placement of proposed new files and functions
- consider known architectural patterns and best practices
- sections on each proposed solution
- relevant files new or existing
- code examples
- pros and cons
- then give reccomendations
## 6 discuss and and allow PM to choose solution
- include discussion section in.md to justify choice
## 7 create actionable plan .md
- discuss
- refine
>> once okayed by PM:
## 8 implement solution
- follow actionable plan.
## 9 test solution
> if fail: 
## 10, revise solution proposal(s)
- discuss with pm and
- choose new solution
## 11, revise action plan 
- discuss plan with pm
- update plan if required
>> once okayed by PM:
## 12, implement revised action plan
## 13, test revised solution
> if pass and PM satisfied: Next step, else: repeat step ## 10
## 14, update documentation
- update today's changelog file - create with ISO date stamp prefix if doesnt exist - provide relevant code links
## 15 suggest improvements to this workflow if needed
task finished




