# InfoBar Usage Guide

## Overview

The InfoBar provides a simple way to display status messages to the user. It's implemented using an event bus pattern to maintain clean architectural boundaries between components while respecting proper UI hierarchy.

## Using the InfoBar

### In Module Views

Module views should implement their own InfoBar widget as part of their panel manager structure. The UpdateDataView provides a good example:

```python
# Set a status message
self.set_status("Files processed successfully")

# Set an error message
self.set_error("Failed to process files")

# Show progress
self.set_progress(current=5, total=10)

# Clear the message
self.clear_info()
```

These methods both update the local InfoBar widget and publish events to the event bus, allowing other components to react if needed.

### In Presenters

Presenters should use the view's interface methods:

```python
# In a presenter method
def process_files(self):
    self.view.set_status("Processing files...")
    # Process files...
    self.view.set_status("Files processed successfully")
```

### From Other Components

If you need to publish messages from components that don't have direct access to the view:

```python
from fm.core.event_bus import Events, global_event_bus

# Show a message
global_event_bus.publish(Events.INFO_MESSAGE, "Your message here")

# Clear the message
global_event_bus.publish(Events.INFO_CLEAR)
```

## How It Works

1. Each module view creates its own InfoBar widget and adds it to its panel manager layout
2. The view subscribes to `INFO_MESSAGE` and `INFO_CLEAR` events
3. When these events are published, the InfoBar is updated
4. The view provides interface methods that both update the local InfoBar and publish events

This approach maintains clean architectural boundaries by:

1. Using proper interfaces between components
2. Respecting UI hierarchy (InfoBar belongs to the panel manager)
3. Avoiding direct widget access
4. Using a pub/sub pattern for communication

## Testing

You can test the InfoBar implementation by running:

```bash
python tests/test_infobar.py
```

This will open a test window with buttons to show and clear messages.
