# Table View Widget Flow Analysis

## Core Components

```python
# Main Table Widget
CustomTableView_v2
└── Toolbar
└── Separator
└── TableViewCore
```

## Data Flow

```python
# From TransactionViewPanel
self.transaction_table.configure(...).set_dataframe(df).show()
```

```python
# In CustomTableView_v2
self._dataframe = Columns.apply_display_names_to_df(df)
self._apply_configuration()
```

```python
# In TableViewCore
@timing_decorator
def set_dataframe(self, df: pd.DataFrame):
    self._model.set_dataframe(df)
```

## Performance Critical Points

1. **Data Transformation**
```python
# Column name conversion
self._dataframe = Columns.apply_display_names_to_df(df)
```

2. **Model Operations**
```python
# DataFrame storage
self._model.set_dataframe(df)
```

3. **Column Management**
```python
# Column visibility and ordering
self.set_display_columns(columns, column_names)
```

4. **UI Configuration**
```python
# Multiple UI operations
self._auto_resize_columns_with_limit()
self._expand_details_column_to_fill_space()
```

## Timing Points

1. **Data Operations**
```python
@timing_decorator
set_dataframe(self, df: pd.DataFrame)
```

2. **Column Operations**
```python
@timing_decorator
set_display_columns(self, columns, column_names)
```

3. **UI Operations**
```python
@timing_decorator
set_editable_columns(self, columns)
```

## Memory Usage Points

1. **DataFrame Copies**
```python
self._dataframe_original = df.copy()
self._dataframe = Columns.apply_display_names_to_df(df)
```

2. **Model Storage**
```python
self._model.set_dataframe(df)
```

3. **Column Mappings**
```python
self._column_mapping = {col: self._dataframe.columns[i] for i, col in enumerate(df.columns)}
```

## UI Rendering Operations

1. **Initial Setup**
```python
# In TableViewCore.__init__
self.setAlternatingRowColors(True)
self.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
self.setSelectionMode(QAbstractItemView.SelectionMode.ExtendedSelection)
self.setSortingEnabled(True)
```

2. **Header Configuration**
```python
header = self.horizontalHeader()
header.setSectionResizeMode(QHeaderView.ResizeMode.Interactive)
header.setStretchLastSection(False)
```

3. **Column Resizing**
```python
@timing_decorator
def _auto_resize_columns_with_limit(self, max_chars=40):
    # Multiple width calculations and resizes
```

## Performance Considerations

1. **Multiple DataFrame Operations**
   - Original DataFrame copy
   - Display name conversion
   - Model storage
   - Column mapping creation

2. **UI Operations**
   - Multiple column resizes
   - Header configuration
   - Sorting setup
   - Selection behavior configuration

3. **Column Management**
   - Display name conversion
   - Column visibility
   - Width calculations
   - Details column expansion
