# Qt Libraries Integration Guide

## Overview

This document covers the integration of third-party Qt libraries into the Flatmate PySide6 application, focusing on libraries that enhance window management, UI components, and user experience.

## Recommended Libraries

### 1. PyQt-Frameless-Window

**Purpose**: Frameless window implementation with reliable window state management

**Installation**:
```bash
pip install PySide6-Frameless-Window
```

**Use Case**: Custom title bars, maximize/restore functionality

**Integration**:
```python
from qframelesswindow import FramelessWindow, StandardTitleBar

class MainWindow(FramelessWindow):
    def __init__(self):
        super().__init__()
        self.setup_ui()
```

**Benefits**:
- ✅ Reliable window state management
- ✅ Cross-platform compatibility
- ✅ Customizable title bars
- ✅ Professional appearance
- ✅ Active maintenance

**Drawbacks**:
- ⚠️ Additional dependency
- ⚠️ Learning curve for advanced customization

### 2. QT-PyQt-PySide-Custom-Widgets

**Purpose**: JSON-configurable custom widgets and window management

**Installation**:
```bash
pip install QT-PyQt-PySide-Custom-Widgets
```

**Use Case**: Rapid UI customization through JSON configuration

**Integration**:
```python
from Custom_Widgets.Widgets import *

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        loadJsonStyle(self, "style.json")
```

**Benefits**:
- ✅ JSON-based configuration
- ✅ No code changes for styling
- ✅ Rich widget library
- ✅ Easy to use
- ✅ Good documentation

**Drawbacks**:
- ⚠️ Less flexible than code-based solutions
- ⚠️ Limited to predefined options

### 3. QDarkStyle

**Purpose**: Dark theme for Qt applications

**Installation**:
```bash
pip install qdarkstyle
```

**Integration**:
```python
import qdarkstyle

app = QApplication(sys.argv)
app.setStyleSheet(qdarkstyle.load_stylesheet_pyside6())
```

### 4. QtAwesome

**Purpose**: Font-based icons for Qt applications

**Installation**:
```bash
pip install qtawesome
```

**Integration**:
```python
import qtawesome as qta

icon = qta.icon('fa5s.home')
button.setIcon(icon)
```

## Integration Strategies

### 1. Gradual Integration

**Phase 1: Testing**
- Create separate test branch
- Install library in virtual environment
- Test basic functionality
- Verify compatibility with existing code

**Phase 2: Core Integration**
- Replace core components (e.g., base window class)
- Maintain existing functionality
- Add library-specific features gradually

**Phase 3: Enhancement**
- Leverage library-specific features
- Remove redundant custom code
- Optimize performance

### 2. Compatibility Considerations

**PySide6 Version Compatibility**:
```python
# Check PySide6 version
from PySide6 import __version__ as pyside_version
print(f"PySide6 version: {pyside_version}")

# Ensure library compatibility
if pyside_version >= "6.0.0":
    from qframelesswindow import FramelessWindow
```

**Python Version Requirements**:
- PyQt-Frameless-Window: Python 3.7+
- QT-PyQt-PySide-Custom-Widgets: Python 3.6+
- Ensure compatibility with current Python version

### 3. Dependency Management

**requirements.txt**:
```
PySide6>=6.0.0
PySide6-Frameless-Window>=0.3.0
QT-PyQt-PySide-Custom-Widgets>=0.8.0
```

**Virtual Environment**:
```bash
python -m venv .venv_fm313
source .venv_fm313/bin/activate  # Linux/macOS
.venv_fm313\Scripts\activate     # Windows
pip install -r requirements.txt
```

## Library-Specific Integration

### PyQt-Frameless-Window Integration

**Step 1: Replace Base Class**
```python
# Before
class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()

# After
class MainWindow(FramelessWindow):
    def __init__(self):
        super().__init__()
```

**Step 2: Customize Title Bar**
```python
class CustomTitleBar(StandardTitleBar):
    def __init__(self, parent):
        super().__init__(parent)
        
        # Apply Flatmate styling
        self.setStyleSheet("""
            StandardTitleBar {
                background-color: #2b2b2b;
                color: white;
            }
        """)

class MainWindow(FramelessWindow):
    def __init__(self):
        super().__init__()
        self.setTitleBar(CustomTitleBar(self))
```

**Step 3: Remove Custom Code**
```python
# Remove these custom implementations:
# - Custom changeEvent handling
# - Window state synchronization
# - Maximize/restore button logic
# - Event timing workarounds
```

### QT-PyQt-PySide-Custom-Widgets Integration

**Step 1: Create Configuration**
```json
{
    "QMainWindow": [{
        "tittle": "Flatmate",
        "icon": ":/icons/flatmate.svg",
        "frameless": true,
        "transluscentBg": false,
        "navigation": [{
            "minimize": "minimize_btn",
            "close": "close_btn",
            "restore": [{
                "buttonName": "maximize_btn",
                "normalIcon": ":/icons/maximize.svg",
                "maximizedIcon": ":/icons/restore.svg"
            }]
        }]
    }]
}
```

**Step 2: Load Configuration**
```python
from Custom_Widgets.Widgets import *

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setupUi(self)
        loadJsonStyle(self, "style.json")
```

## Testing Integration

### 1. Unit Tests

```python
import unittest
from PySide6.QtWidgets import QApplication
from qframelesswindow import FramelessWindow

class TestFramelessIntegration(unittest.TestCase):
    def setUp(self):
        self.app = QApplication([])
        self.window = FramelessWindow()
    
    def test_window_creation(self):
        self.assertIsNotNone(self.window)
    
    def test_maximize_restore(self):
        self.window.showMaximized()
        self.assertTrue(self.window.isMaximized())
        
        self.window.showNormal()
        self.assertFalse(self.window.isMaximized())
```

### 2. Integration Tests

```python
def test_title_bar_integration():
    """Test that custom title bar works with library"""
    window = MainWindow()
    window.show()
    
    # Test maximize button
    maximize_btn = window.title_bar.window_controls.maximize_btn
    maximize_btn.click()
    
    # Verify window state
    assert window.isMaximized()
```

### 3. Manual Testing Checklist

- [ ] Window opens correctly
- [ ] Title bar displays properly
- [ ] Minimize button works
- [ ] Maximize button works (single click)
- [ ] Restore button works (single click)
- [ ] Close button works
- [ ] Window can be dragged
- [ ] Window can be resized
- [ ] Cross-platform compatibility

## Performance Considerations

### 1. Library Overhead

**PyQt-Frameless-Window**:
- Minimal overhead
- Native Qt implementation
- Efficient event handling

**QT-PyQt-PySide-Custom-Widgets**:
- JSON parsing overhead (one-time)
- Additional widget layers
- Generally good performance

### 2. Memory Usage

Monitor memory usage after integration:
```python
import psutil
import os

process = psutil.Process(os.getpid())
memory_mb = process.memory_info().rss / 1024 / 1024
print(f"Memory usage: {memory_mb:.1f} MB")
```

### 3. Startup Time

Measure application startup time:
```python
import time

start_time = time.time()
app = QApplication(sys.argv)
window = MainWindow()
window.show()
startup_time = time.time() - start_time
print(f"Startup time: {startup_time:.2f} seconds")
```

## Troubleshooting

### Common Issues

**1. Import Errors**
```python
# Error: ModuleNotFoundError: No module named 'qframelesswindow'
# Solution: Install in correct virtual environment
pip install PySide6-Frameless-Window
```

**2. Version Conflicts**
```python
# Error: Incompatible PySide6 versions
# Solution: Check version compatibility
pip list | grep PySide6
pip install --upgrade PySide6
```

**3. Styling Issues**
```python
# Error: Custom styles not applied
# Solution: Ensure proper style sheet loading order
self.setStyleSheet(custom_styles)  # Apply after library initialization
```

### Debug Information

Enable debug output for libraries:
```python
import logging
logging.basicConfig(level=logging.DEBUG)

# Library-specific debug flags
os.environ['QT_LOGGING_RULES'] = 'qt.qpa.window=true'
```

## Migration Checklist

### Pre-Migration
- [ ] Backup current implementation
- [ ] Create test branch
- [ ] Document current functionality
- [ ] Identify custom code to remove

### During Migration
- [ ] Install chosen library
- [ ] Replace base classes
- [ ] Test basic functionality
- [ ] Adapt custom components
- [ ] Remove redundant code

### Post-Migration
- [ ] Comprehensive testing
- [ ] Performance verification
- [ ] Documentation updates
- [ ] Code cleanup
- [ ] Deployment testing

## Conclusion

Third-party Qt libraries can significantly improve the reliability and maintainability of PySide6 applications. For Flatmate, PyQt-Frameless-Window is the recommended choice for resolving window state management issues while preserving the existing custom design.

The key to successful integration is gradual implementation, thorough testing, and proper dependency management.
