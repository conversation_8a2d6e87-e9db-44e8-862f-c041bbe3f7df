"""
Cached SQLite Transaction Repository

A caching wrapper around SQLiteTransactionRepository that implements transparent
in-memory caching for improved performance while maintaining the same interface.
"""
from __future__ import annotations

import threading
import time
from typing import Dict, List, Optional, Any
from datetime import datetime

from fm.core.services.logger import log
from .transaction_repository import ITransactionRepository, Transaction, ImportResult
from .sqlite_repository import SQLiteTransactionRepository


class CachedSQLiteRepository(ITransactionRepository):
    """
    Cached implementation of ITransactionRepository.
    
    Provides transparent caching over SQLiteTransactionRepository while
    maintaining the exact same interface. Cache is warmed synchronously
    during initialization and falls back to direct database access on
    cache failures.
    """
    
    def __init__(self):
        """Initialize the cached repository."""
        self._db_repo = SQLiteTransactionRepository()
        self._cache = {}
        self._cache_ready = False
        self._cache_lock = threading.RLock()  # Thread-safe cache access
        self._last_cache_time = None
        
        log.debug("CachedSQLiteRepository initialized")
    
    def warm_cache(self) -> bool:
        """
        Synchronously load cache during initialization.
        
        Returns:
            True if cache was successfully warmed, False otherwise
        """
        log.info("Warming transaction cache...")
        start_time = time.time()
        
        try:
            with self._cache_lock:
                # Load all transactions from database
                all_transactions = self._db_repo.get_transactions()
                
                if not all_transactions:
                    log.warning("No transactions found in database for caching")
                    self._cache['all_transactions'] = []
                    self._cache['unique_accounts'] = []
                    self._cache_ready = True
                    return True
                
                # Cache all transactions
                self._cache['all_transactions'] = all_transactions
                
                # Cache unique account numbers
                unique_accounts = sorted(list(set(
                    t.account for t in all_transactions if hasattr(t, 'account') and t.account
                )))
                self._cache['unique_accounts'] = unique_accounts
                
                # Cache statistics
                self._cache['statistics'] = self._db_repo.get_statistics()
                
                self._cache_ready = True
                self._last_cache_time = datetime.now()
                
                elapsed = time.time() - start_time
                log.info(f"Cache warmed successfully: {len(all_transactions)} transactions, "
                        f"{len(unique_accounts)} unique accounts in {elapsed:.2f}s")
                return True
                
        except Exception as e:
            log.error(f"Cache warming failed: {e}", exc_info=True)
            with self._cache_lock:
                self._cache_ready = False
                self._cache.clear()
            return False
    
    def is_cache_ready(self) -> bool:
        """Check if cache is ready for use."""
        with self._cache_lock:
            return self._cache_ready
    
    def get_cache_info(self) -> Dict[str, Any]:
        """Get information about cache state."""
        with self._cache_lock:
            if not self._cache_ready:
                return {"ready": False}
            
            return {
                "ready": True,
                "total_transactions": len(self._cache.get('all_transactions', [])),
                "unique_accounts": len(self._cache.get('unique_accounts', [])),
                "last_updated": self._last_cache_time,
                "cached_items": list(self._cache.keys())
            }
    
    # ITransactionRepository interface implementation
    
    def add_transactions(self, transactions: List[Transaction]) -> ImportResult:
        """Add new transactions to the repository."""
        result = self._db_repo.add_transactions(transactions)
        
        # Invalidate cache if transactions were added
        if result.added_count > 0:
            log.debug(f"Invalidating cache due to {result.added_count} new transactions")
            with self._cache_lock:
                self._cache_ready = False
                self._cache.clear()
        
        return result
    
    def get_transactions(self, filters: Optional[Dict] = None) -> List[Transaction]:
        """Retrieve transactions matching the filters."""
        # If no filters and cache is ready, return cached data
        if not filters and self.is_cache_ready():
            with self._cache_lock:
                cached_transactions = self._cache.get('all_transactions', [])
                log.debug(f"Cache hit: returning {len(cached_transactions)} transactions")
                return cached_transactions.copy()  # Return copy to prevent modification
        
        # For filtered queries or cache miss, use database
        log.debug("Cache miss or filtered query - using database")
        return self._db_repo.get_transactions(filters)
    
    def update_transaction(self, transaction_id: Optional[int], data: Dict) -> bool:
        """Update a specific transaction."""
        result = self._db_repo.update_transaction(transaction_id, data)
        
        # Invalidate cache if update was successful
        if result:
            log.debug(f"Invalidating cache due to transaction update: {transaction_id}")
            with self._cache_lock:
                self._cache_ready = False
                self._cache.clear()
        
        return result
    
    def delete_transaction(self, transaction_id: int) -> bool:
        """Mark a transaction as deleted."""
        result = self._db_repo.delete_transaction(transaction_id)
        
        # Invalidate cache if deletion was successful
        if result:
            log.debug(f"Invalidating cache due to transaction deletion: {transaction_id}")
            with self._cache_lock:
                self._cache_ready = False
                self._cache.clear()
        
        return result
    
    def delete_all_transactions(self) -> int:
        """Mark all transactions as deleted."""
        count = self._db_repo.delete_all_transactions()
        
        # Invalidate cache if any transactions were deleted
        if count > 0:
            log.debug(f"Invalidating cache due to {count} transaction deletions")
            with self._cache_lock:
                self._cache_ready = False
                self._cache.clear()
        
        return count
    
    def get_statistics(self) -> Dict:
        """Get statistics about the stored transactions."""
        # Use cached statistics if available
        if self.is_cache_ready():
            with self._cache_lock:
                cached_stats = self._cache.get('statistics')
                if cached_stats:
                    log.debug("Cache hit: returning cached statistics")
                    return cached_stats.copy()
        
        # Cache miss - get from database
        log.debug("Cache miss for statistics - using database")
        return self._db_repo.get_statistics()
    
    def add_transactions_from_df(self, df, source_file: Optional[str] = None) -> ImportResult:
        """Add new transactions from a pandas DataFrame."""
        result = self._db_repo.add_transactions_from_df(df, source_file)
        
        # Invalidate cache if transactions were added
        if result.added_count > 0:
            log.debug(f"Invalidating cache due to {result.added_count} new transactions from DataFrame")
            with self._cache_lock:
                self._cache_ready = False
                self._cache.clear()
        
        return result
    
    # Additional convenience methods for common cached operations
    
    def get_unique_account_numbers(self) -> List[str]:
        """Get unique account numbers (cached when possible)."""
        if self.is_cache_ready():
            with self._cache_lock:
                cached_accounts = self._cache.get('unique_accounts', [])
                log.debug(f"Cache hit: returning {len(cached_accounts)} unique accounts")
                return cached_accounts.copy()
        
        # Cache miss - calculate from database
        log.debug("Cache miss for unique accounts - calculating from database")
        transactions = self._db_repo.get_transactions()
        unique_accounts = sorted(list(set(
            t.account for t in transactions if hasattr(t, 'account') and t.account
        )))
        return unique_accounts
    
    def refresh_cache(self) -> bool:
        """Manually refresh the cache."""
        log.info("Manually refreshing cache...")
        with self._cache_lock:
            self._cache_ready = False
            self._cache.clear()
        return self.warm_cache()
