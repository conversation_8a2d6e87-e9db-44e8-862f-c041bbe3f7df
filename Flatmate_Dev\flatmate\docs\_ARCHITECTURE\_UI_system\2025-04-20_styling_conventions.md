# Styling Conventions in FlatMate

This document outlines the styling conventions used in the FlatMate application, focusing on property-based QSS styling.

## Core Principles

1. **Separation of concerns**: UI styling is separated from code
2. **Consistency**: All widgets follow the same styling approach
3. **Maintainability**: Styles are defined in central QSS files
4. **Clarity**: Self-documenting styling approach

## Color System

Colors are defined in `palette.qss` as CSS variables:

```css
:root {
    /* Primary Colors */
    --color-primary: #3B8A45;
    
    /* Text Colors */
    --color-text-primary: #FFFFFF;
    --color-text-secondary: #B0B0B0;
    --color-calm-white: #CCCCCC;  /* Softer white for subheadings and icons */
}
```

## Property-Based Styling

Instead of using object names (`setObjectName`), we use properties (`setProperty`) for styling:

### Example:

```python
# In Python code
button.setProperty("type", "action_btn")
```

```css
/* In QSS file */
QPushButton[type="action_btn"] {
    background-color: var(--color-primary);
    color: white;
    border-radius: 4px;
    padding: 8px 16px;
}
```

## Advantages of Property-Based Styling

1. **Reusability**: Multiple widgets can share the same style
2. **Semantic meaning**: Properties describe the purpose of the widget
3. **Flexibility**: Easier to change styles without modifying code
4. **Hierarchical**: Can combine with class selectors for more specific styling

## Common Properties

| Property | Values | Description |
|----------|--------|-------------|
| `type` | `"action_btn"`, `"secondary_btn"`, `"danger_btn"` | Button styling variants |
| `class` | `"nav_button"`, `"form_field"`, `"data_display"` | Widget category |
| `state` | `"success"`, `"error"`, `"warning"`, `"info"` | State-based styling |
| `size` | `"small"`, `"medium"`, `"large"` | Size variants |

## Examples

### Buttons

```python
# Primary action button
action_btn = QPushButton("Save")
action_btn.setProperty("type", "action_btn")

# Secondary button
cancel_btn = QPushButton("Cancel")
cancel_btn.setProperty("type", "secondary_btn")
```

### Navigation Buttons

```python
# Navigation button
nav_btn = QToolButton()
nav_btn.setProperty("class", "nav_button")
```

### Labels

```python
# Subheading label
label = QLabel("Settings")
label.setProperty("type", "subheading")
```

## QSS Selectors

```css
/* Type-based selector */
QPushButton[type="action_btn"] {
    /* Styles for action buttons */
}

/* Class-based selector */
QToolButton[class="nav_button"] {
    /* Styles for navigation buttons */
}

/* Combined selectors */
QLabel[type="subheading"][state="error"] {
    /* Styles for error subheadings */
}
```

## Best Practices

1. Always use properties instead of object names for styling
2. Use semantic property names that describe the purpose
3. Keep all color definitions in `palette.qss`
4. Use CSS variables for colors and sizes
5. Document new properties in this guide
