#!/usr/bin/env python3
"""
Main entry point for the FlatMate application.
"""

import sys
from pathlib import Path

from PySide6.QtWidgets import QApplication, QSplashScreen
from PySide6.QtGui import QPixmap
from PySide6.QtCore import Qt
from .core.config import config
from .core.config.paths import AppPaths
from .core.config.keys import ConfigKeys
from .core.services.event_bus import global_event_bus
from .core.services.logger import log
from .gui.main_window import MainWindow
from .module_coordinator import ModuleCoordinator


def initialize_application() -> tuple:
    """
    Comprehensive application initialization.

    Sets up logging, configuration, and ensures required directories exist.

    Returns:
        tuple: (QApplication, MainWindow, ModuleCoordinator) - The main application objects
    """
    # 0. Ensure all required directories exist
    AppPaths.ensure_directories()

    # 1. Logging is now handled by the log object, which configures itself.
    log.info("Application starting...")
    
    try:
        # 2. Publish debug mode event if enabled
        if config.get_value(ConfigKeys.App.DEBUG_MODE, False):
            global_event_bus.publish('debug_mode_enabled')
        
        # Directory creation is handled by SystemPaths/UserPaths in config.py and by logger.py for its specific log directory.
        # The loop for core_paths has been removed.

        # 3. Initialize and show the main application window
        app = QApplication(sys.argv)
        
        # 5. Load and apply styles
        from .gui.styles import apply_styles
        apply_styles(app)
        
        # 4. Create Main Window
        main_window = MainWindow()
        main_window.resize(1200, 800)  # Set initial size 
        main_window.show()
        
        # 5. Create splash screen
        splash_path = AppPaths.RESOURCES_DIR / "splash" / "unify_transparent_bg-Splash.png"
        splash_pixmap = QPixmap(str(splash_path))

        if not splash_pixmap.isNull():
            splash = QSplashScreen(splash_pixmap)
            splash.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.FramelessWindowHint)
            splash.show()
            splash.showMessage("Initializing Flatmate...", Qt.AlignBottom | Qt.AlignCenter, Qt.white)
            app.processEvents()
        else:
            log.warning(f"Could not load splash screen from {splash_path}")
            splash = None

        # 6. Create and initialize coordinator
        log.info("\n=== Setting up Module Coordinator ===")

        if splash:
            splash.showMessage("Setting up modules...", Qt.AlignBottom | Qt.AlignCenter, Qt.white)
            app.processEvents()

        coordinator = ModuleCoordinator(main_window)

        if splash:
            splash.showMessage("Loading transaction data...", Qt.AlignBottom | Qt.AlignCenter, Qt.white)
            app.processEvents()

        coordinator.initialize_modules()

        if splash:
            splash.showMessage("Finalizing interface...", Qt.AlignBottom | Qt.AlignCenter, Qt.white)
            app.processEvents()

        # Set the coordinator in the main window
        main_window.set_module_manager(coordinator)

        # 7. Start coordinator
        coordinator.start()  # Start coordinator to transition to home

        # 8. Hide splash screen and show main window
        main_window.show()

        if splash:
            splash.finish(main_window)  # Hide splash screen smoothly

        log.info("\n=== Application Ready ===")
        return app, main_window, coordinator
        
    except Exception as e:
        log.critical(f"Fatal error during application initialization: {e}")
        import traceback
        log.critical(f"Exception details: {traceback.format_exc()}")
        raise


def main():
    """Application entry point."""
    app, window, coordinator = initialize_application()
    return app.exec()


if __name__ == "__main__":
    sys.exit(main())
