#!/usr/bin/env python3
"""
Main entry point for the FlatMate application.
"""

import sys
from pathlib import Path

from PySide6.QtWidgets import QApplication
from .core.config import config
from .core.config.paths import AppPaths
from .core.config.keys import ConfigKeys
from .core.services.event_bus import global_event_bus
from .core.services.logger import log
from .core.data_services.db_io_service import DBIOService
from .gui.main_window import MainWindow
from .module_coordinator import ModuleCoordinator


def initialize_application() -> tuple:
    """
    Comprehensive application initialization.

    Sets up logging, configuration, and ensures required directories exist.

    Returns:
        tuple: (QApplication, MainWindow, ModuleCoordinator) - The main application objects
    """
    # 0. Ensure all required directories exist
    AppPaths.ensure_directories()

    # 1. Logging is now handled by the log object, which configures itself.
    log.info("Application starting...")
    
    try:
        # 2. Publish debug mode event if enabled
        if config.get_value(ConfigKeys.App.DEBUG_MODE, False):
            global_event_bus.publish('debug_mode_enabled')
        
        # Directory creation is handled by SystemPaths/UserPaths in config.py and by logger.py for its specific log directory.
        # The loop for core_paths has been removed.

        # 3. Initialize and show the main application window
        app = QApplication(sys.argv)
        
        # 5. Load and apply styles
        from .gui.styles import apply_styles
        apply_styles(app)
        
        # 4. Create Main Window
        main_window = MainWindow()
        main_window.resize(1200, 800)  # Set initial size 
        main_window.show()
        
        # 5. Create and initialize coordinator
        # 6. Initialize database service and cache for instant access
        log.info("\n=== Initializing Database Service ===")
        db_service = DBIOService()
        try:
            cache_success = db_service.initialize_cache(show_progress=True)

            if cache_success:
                cache_info = db_service.get_cache_info()
                log.info(f"Database cache initialized: {cache_info['total_transactions']} transactions, "
                        f"{cache_info['memory_usage_mb']:.1f}MB memory usage")
            else:
                log.warning("Database cache initialization failed - modules will query database directly")

        except Exception as e:
            log.warning(f"Database cache initialization failed: {e} - modules will query database directly")

        # 7. Create and initialize coordinator, injecting the db_service
        log.info("\n=== Setting up Module Coordinator ===")
        coordinator = ModuleCoordinator(main_window, db_service)
        coordinator.initialize_modules()
        
        # Set the coordinator in the main window
        main_window.set_module_manager(coordinator)

        # 7. Start coordinator
        coordinator.start()  # Start coordinator to transition to home

        log.info("\n=== Application Ready ===")
        return app, main_window, coordinator
        
    except Exception as e:
        log.critical(f"Fatal error during application initialization: {e}")
        import traceback
        log.critical(f"Exception details: {traceback.format_exc()}")
        raise


def main():
    """Application entry point."""
    app, window, coordinator = initialize_application()
    return app.exec()


if __name__ == "__main__":
    sys.exit(main())
