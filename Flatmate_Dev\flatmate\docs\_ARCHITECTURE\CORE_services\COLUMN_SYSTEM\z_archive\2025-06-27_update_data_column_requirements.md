# Update Data Module Column Requirements

## Overview
This document outlines how the `update_data` module uses the column system in Flatmate, serving as a reference for the column system unification project. The module is responsible for importing bank statement data from various formats and converting it to a standardized format.

## Column System Usage

### Source of Truth
The module exclusively uses the canonical `StandardColumns` enum from `fm.core.data_services.standards.fm_standard_columns` for all column operations. This aligns with the recommended approach during migration.

### Key Files and Their Column Usage

#### 1. Statement Handlers
- **Base Handler** (`_base_statement_handler.py`):
  - Defines the mapping framework from source CSV columns to target `StandardColumns`
  - Uses `target_col_names: List[Optional[StandardColumns]]` for column mapping
  - Implements column reordering based on `StandardColumns` enum order
  - Handles date standardization for the `DATE` column

- **Bank-Specific Handlers**:
  - Each handler (e.g., `kiwibank_basic_csv_handler.py`) defines mappings from source columns to `StandardColumns`
  - Example mapping from `KiwibankBasicCSVHandler`:
    ```python
    target_col_names=[
        StandardColumns.DATE,
        StandardColumns.DETAILS,
        StandardColumns.EMPTY_COLUMN,
        StandardColumns.AMOUNT,
        StandardColumns.BALANCE,
    ]
    ```

#### 2. Pipeline (`dw_pipeline.py`)
- Uses `StandardColumns` for:
  - Validating core data structure
  - Standardizing date formats
  - Database record updates
  - Column reordering and selection

#### 3. Director (`dw_director.py`)
- Orchestrates the processing pipeline
- References `StandardColumns` for date column handling
- Manages the overall flow of data transformation

## Required Columns
The update_data module requires these essential columns:

1. **Required Core Columns**:
   - `DATE` - Transaction date
   - `DETAILS` - Transaction description
   - `AMOUNT` - Transaction amount

2. **At Least One of These Must Exist**:
   - `BALANCE` - Account balance after transaction
   - `UNIQUE_ID` - Unique transaction identifier
   
   This requirement is critical for transaction deduplication and validation.

3. **Additional Columns**:
   - `ACCOUNT` - Account number (added during processing if not in source)
   - `SOURCE_FILENAME` - Original file name (added during processing)

## Column Operations

### 1. Mapping
Source CSV columns are mapped to standardized `StandardColumns` enum values:
```python
# Example from KiwibankBasicCSVHandler
target_col_names=[
    StandardColumns.DATE,
    StandardColumns.DETAILS,
    StandardColumns.EMPTY_COLUMN,
    StandardColumns.AMOUNT,
    StandardColumns.BALANCE,
]
```

### 2. Validation
The pipeline validates that all required columns exist and that at least one of either UNIQUE_ID or BALANCE columns is present:
```python
# From validate_core_data_structure()
required_cols = [
    StandardColumns.DATE.value,
    StandardColumns.DETAILS.value,
    StandardColumns.AMOUNT.value,
]

# At least one of these columns must exist
optional_required_cols = [
    StandardColumns.BALANCE.value,
    StandardColumns.UNIQUE_ID.value,
]
```

### 3. Type Conversion
Date columns are converted to a standardized format:
```python
# From _standardize_dates()
date_col = StandardColumns.DATE.value
df[date_col] = convert_df_dates(df[date_col], self.column_attrs.date_format)
```

### 4. Reordering
Columns are reordered based on the `StandardColumns` enum order:
```python
# From _reorder_columns()
standard_order = [col.value for col in StandardColumns]
```

## Database Integration
The module updates the database with processed transactions, using `StandardColumns` to identify key fields:
```python
# From update_database_records()
date_col = StandardColumns.DATE.value
```

## Migration Considerations

1. **Compatibility Requirements**:
   - The unified column system must maintain the same enum values for all columns used by update_data
   - Any changes to column names would require updates to all statement handlers

2. **Enhancement Opportunities**:
   - Add type validation for columns (especially DATE and AMOUNT)
   - Add category metadata to distinguish system vs. user columns
   - Improve documentation of column purposes and relationships

3. **Potential Risks**:
   - Changes to column name values could break statement handlers
   - Changes to date handling could affect file processing
   - Database integration depends on consistent column naming

## Conclusion
The update_data module demonstrates a clean implementation using the canonical column system exclusively. It will serve as a good reference for how other modules should interact with the unified column system. During migration, special attention should be paid to maintaining compatibility with the existing column names and values to ensure continued functionality.
