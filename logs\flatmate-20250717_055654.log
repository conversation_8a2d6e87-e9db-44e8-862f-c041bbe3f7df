2025-07-17 05:56:54 - [fm.core.services.master_file_service] [INFO] - MasterFileService initialized
2025-07-17 05:56:55 - [fm.core.services.cache_service] [INFO] - CacheService initialized
2025-07-17 05:56:58 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-07-17 05:56:58 - [CoopStandardCSVHandler] [DEBUG] - Initialized CoopStandardCSVHandler
2025-07-17 05:56:58 - [ASBStandardCSVHandler] [DEBUG] - Initialized ASBStandardCSVHandler
2025-07-17 05:56:59 - [fm.modules.update_data.services.local_services] [DEBUG] - UpdateDataServices initialized
2025-07-17 05:56:59 - [fm.core.config.base_local_config_v2] [DEBUG] - Loaded config hierarchy for categorize: 0 component defaults, 18 user preferences
2025-07-17 05:56:59 - [fm.core.config.base_local_config_v2] [DEBUG] - CategorizeConfig for categorize initialized.
2025-07-17 05:57:01 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-07-17 05:57:01 - [CoopStandardCSVHandler] [DEBUG] - Initialized CoopStandardCSVHandler
2025-07-17 05:57:01 - [ASBStandardCSVHandler] [DEBUG] - Initialized ASBStandardCSVHandler
2025-07-17 05:57:02 - [src.fm.modules.update_data.services.local_services] [DEBUG] - UpdateDataServices initialized
2025-07-17 05:57:04 - [main] [INFO] - Application starting...
2025-07-17 05:57:10 - [main] [INFO] - 
=== Initializing Core Services ===
2025-07-17 05:57:10 - [main] [CRITICAL] - Fatal error during application initialization: 'ConfigManager' object has no attribute 'initialize'
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\main.py", line 74, in initialize_application
    config.initialize()
    ^^^^^^^^^^^^^^^^^
AttributeError: 'ConfigManager' object has no attribute 'initialize'
2025-07-17 05:57:10 - [main] [CRITICAL] - Exception details: Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\main.py", line 74, in initialize_application
    config.initialize()
    ^^^^^^^^^^^^^^^^^
AttributeError: 'ConfigManager' object has no attribute 'initialize'
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\main.py", line 74, in initialize_application
    config.initialize()
    ^^^^^^^^^^^^^^^^^
AttributeError: 'ConfigManager' object has no attribute 'initialize'
