# Action Plan: Threaded Pre-loading for Performance

**Generated:** 2025-07-15

---

## 1. Objective

To completely eliminate the ~2.7-second UI freeze that occurs when navigating to the **Categorize module**. This will be achieved by moving the expensive data preparation and rendering logic from the point of navigation to a non-blocking, background thread that runs during application startup.

**Target Outcome:** Navigation to the Categorize module should feel instantaneous (< 0.2 seconds), and the application's UI should remain fully responsive at all times.

---

## 2. Core Strategy

The solution involves creating a dedicated service that prepares all the necessary data for the Categorize view in the background. The UI will then simply retrieve the pre-prepared data, bypassing the slow processing steps during user interaction.

1.  **On Application Startup:** A background worker will be launched to fetch, categorize, and sort all transaction data.
2.  **During User Navigation:** The `CategorizePresenter` will request the completed data from the service. Because the data is already processed, setting the table view will be significantly faster.

---

## 3. Implementation Plan

This plan synthesizes the designs from the `PROPOSED_SOLUTIONS` documents into a concrete set of actions.

### **Phase 1: Create the `DataPreparationService`**

This new service will orchestrate the background processing.

**Action:** Create a new file: `flatmate/src/fm/core/services/data_preparation_service.py`

**Contents:**
-   A singleton `DataPreparationService` class to manage the process and store the result.
-   A `DataPreparationWorker` class (using `QRunnable` and `QThreadPool`) that contains the actual data processing logic.
-   The worker will:
    1.  Fetch the full transaction DataFrame from the already-implemented `DBCachingService`.
    2.  Apply the `TransactionCategorizer` logic to the DataFrame.
    3.  Perform a default sort (e.g., by date).
    4.  Emit a `finished` signal with the prepared DataFrame upon completion, or an `error` signal if something goes wrong.
-   The service will have a public method `get_categorize_dataframe()` and a flag `is_ready` for the UI to check.

### **Phase 2: Integrate the Service into the Application**

The service must be initialized at startup.

**Action 1:** Make the service easily accessible.
-   **File:** `flatmate/src/fm/core/services/__init__.py`
-   **Change:** Import and expose the singleton instance of `data_preparation_service`.

**Action 2:** Launch the background process.
-   **File:** `flatmate/src/fm/main.py`
-   **Change:** In the `initialize_application` sequence, after the database cache has been successfully loaded, call `data_preparation_service.start_preparation()`.

### **Phase 3: Refactor `CategorizePresenter` to Use Pre-Prepared Data**

This is the final and most critical step, simplifying the presenter and leveraging the background work.

**Action:** Modify the `_handle_load_db` method in `flatmate/src/fm/modules/categorize/cat_presenter.py`.

**Refactoring Steps:**
1.  Import the `data_preparation_service`.
2.  Completely replace the existing logic inside `_handle_load_db`.
3.  The new logic will:
    -   Check if `data_preparation_service.is_ready` is `True`.
    -   If not ready, publish a message to the `InfoBar` (e.g., "Preparing data, please wait...") and implement a brief, non-blocking wait.
    -   Call `data_preparation_service.get_categorize_dataframe()` to get the pre-processed data.
    -   If the data is valid, set it directly to the `TableView`.
    -   Update the `InfoBar` with the final status (e.g., "Displayed X transactions").

---

## 4. Success Metrics

-   **Primary Metric:** The measured time for navigating to and displaying the Categorize module must be **under 0.2 seconds**.
-   **Secondary Metric:** The application's main window must remain interactive and responsive during the entire startup process (i.e., no UI freezing).
-   **Functional Metric:** All existing functionality within the Categorize module (filtering, sorting, saving changes) must remain fully operational.
