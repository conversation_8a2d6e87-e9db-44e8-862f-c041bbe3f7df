# Data Pipeline Overview: From CSV to Database

This document provides a comprehensive overview of the data processing pipeline, which is responsible for taking raw bank statement CSV files, processing them, and updating the central SQLite database.

## High-Level Goal

The primary goal of the pipeline is to create a standardized, clean, and de-duplicated central transaction ledger from multiple, variably-formatted bank CSV files. 

---

## Key Components & Their Roles

The pipeline is composed of several key modules, each with a distinct responsibility:

- **[`dw_director.py`](file:///c:/Users/<USER>/_DEV/__PROJECTS/Flatmate_Dev/flatmate/src/fm/modules/update_data/utils/dw_director.py)** (The Orchestrator): This is the main entry point. It takes a `job_sheet` (containing file paths and configuration) and orchestrates the entire workflow by calling the necessary functions from `dw_pipeline.py`.

- **[`dw_pipeline.py`](file:///c:/Users/<USER>/_DEV/__PROJECTS/Flatmate_Dev/flatmate/src/fm/modules/update_data/utils/dw_pipeline.py)** (The Core Logic): Contains the high-level functions that define the sequence of operations: loading files, processing with handlers, merging data, and updating the database.

- **[`file_utils.py`](file:///c:/Users/<USER>/_DEV/__PROJECTS/Flatmate_Dev/flatmate/src/fm/modules/update_data/utils/file_utils.py)** (The File Loader): Its primary role is to load a CSV file from disk into a raw pandas DataFrame, attaching basic metadata like the filename.

- **[`statement_handlers/`](file:///c:/Users/<USER>/_DEV/__PROJECTS/Flatmate_Dev/flatmate/src/fm/modules/update_data/utils/statement_handlers/)** (The Parsers): This directory contains bank-specific handlers (e.g., `asb_standard_csv_handler.py`). Each handler contains the unique rules for parsing a specific bank's CSV format, including where metadata ends, where the header row is, and how to map its columns to our standard format.

- **[`DBIOService`](file:///c:/Users/<USER>/_DEV/__PROJECTS/Flatmate_Dev/flatmate/src/fm/core/data_services/db_io_service.py)** (The Business Logic Layer): This is a high-level service that acts as the primary API for the database. It is responsible for enforcing business rules (e.g., data validation), preparing data for insertion (e.g., date formatting), and coordinating with the data access layer. It is decoupled from the database itself.

- **[`ITransactionRepository`](file:///c:/Users/<USER>/_DEV/__PROJECTS/Flatmate_Dev/flatmate/src/fm/core/database/repository/transaction_repository.py)** (The Data Access Contract): This is an abstract interface that defines the contract for what a database repository must be able to do (e.g., `add_transactions_from_df`, `get_transactions`).

- **`SQLiteTransactionRepository`** (The Data Access Layer): This is the concrete implementation of the `ITransactionRepository` interface for SQLite. Its sole responsibility is to execute raw database operations: inserting, updating, and querying records. (Note: Could not locate this file - it may have been renamed or moved)

---

## The Processing Flow

The pipeline executes in the following sequence:

1.  **Initiation:** `dw_director` is called with a list of file paths.
2.  **Raw File Loading:** For each file, `file_utils.load_csv_to_df` reads the *entire* CSV into a DataFrame **without headers** (`header=None`). This prevents parsing errors on files with metadata at the top.
3.  **Handler Identification:** The raw DataFrame is passed to `get_handler`, which inspects its content to find the correct `StatementHandler` for that bank format.
4.  **Data Formatting:** The handler's `format_df` method is called. This is where the raw data is intelligently processed:
    - It uses its configuration to slice off the metadata rows.
    - It identifies the correct row to use as the header and assigns it to the DataFrame's columns.
    - It renames the bank-specific columns to the application's standard column names (e.g., `StandardColumns.DATE`).
    - It performs any other custom, bank-specific transformations.
5.  **Merging:** All the individually formatted DataFrames are merged into a single, large DataFrame.
6.  **Database Update:** The final, merged DataFrame is passed to `dw_pipeline.update_database_from_df`.
7.  **Service Instantiation:** This function instantiates the `SQLiteTransactionRepository` and injects it into the `DBIOService` constructor.
8.  **Business Logic & Validation:** `DBIOService.update_database` is called. It performs final data preparation and enforces critical business rules, such as ensuring every transaction has either a `balance` or `unique_id` column.
9.  **Database Insertion:** The validated, clean DataFrame is passed to `SQLiteTransactionRepository.add_transactions_from_df`, which handles the final step of inserting new records and identifying/skipping duplicates based on a content hash.

---

## Key Architectural Improvements

Several key changes were made to make this pipeline more robust, testable, and maintainable:

1.  **Robust CSV Parsing:** We solved a critical "chicken-and-egg" problem where the program would crash trying to parse a file before knowing *how* to parse it. By loading the file as raw data first and deferring the parsing logic to the specific handler, the system can now reliably process files with any number of metadata rows.

2.  **Dependency Injection & Decoupling:** `DBIOService` was refactored to depend on the `ITransactionRepository` interface, not the concrete `SQLiteTransactionRepository`. The repository is now **injected** into the service's constructor. This decouples the business logic from the database, which dramatically improves testability and makes it easier to swap database backends in the future.

3.  **Clear Separation of Concerns:** The roles of the service and repository are now crystal clear:
    - **`DBIOService`:** Handles *business logic* (What data is valid?).
    - **`Repository`:** Handles *data access* (How is data stored?).

4.  **Comprehensive Testing:**
    - **Unit Tests:** We created dedicated unit tests for `DBIOService` using a mock repository. This allows us to verify its business rules in complete isolation from the database.
    - **End-to-End Tests:** We created and successfully ran an end-to-end test (`test_pipeline.py`) that validates the entire pipeline's functionality with a real-world CSV file, ensuring all components work together correctly.

Current to 2025-06-28 @ 15:40:56