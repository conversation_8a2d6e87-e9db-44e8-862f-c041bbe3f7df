# Proposed Solution: Optimizing `TransactionViewPanel` Performance

**Date:** 2025-07-16

## 1. Summary

This document proposes a solution to the performance issue identified in the `CategorizePresenter`, where loading transactions takes over 4 seconds. 

**Analysis Report:** [Performance Analysis: `CategorizePresenter._handle_load_db`](./2025-07-16_categorize_presenter_performance_analysis.md)

The analysis concluded that the bottleneck is not data processing, but the UI update logic in `TransactionViewPanel.set_transactions`. The method unnecessarily re-configures the entire table component on every data load, causing significant delays.

## 2. Proposed Solution

The solution is to refactor `TransactionViewPanel` to separate the one-time table configuration from the data-loading process.

-   **Configuration (`_init_ui`)**: The `self.transaction_table.configure()` method will be called only once when the panel is initialized.
-   **Data Loading (`set_transactions`)**: This method will be simplified to only call `self.transaction_table.set_dataframe(df)`, making it lightweight and fast.

### Affected File

-   `c:/Users/<USER>/_DEV/__PROJECTS/Flatmate_Dev/flatmate/src/fm/modules/categorize/_view/components/center_panel/transaction_view_panel.py`

### Code Changes

#### A. In `_init_ui()` method:

The `self.transaction_table.configure()` call will be moved here from `set_transactions`.

**Current Code (`_init_ui`):**
```python
# ... inside _init_ui
self.transaction_table = TableView()

# Configure the table with sensible defaults
default_visible = config.get_value('categorize.display.default_visible_columns', [])
self.transaction_table.configure(
    auto_size_columns=True,
    max_column_width=40,
    editable_columns=['tags'],  # Only tags are editable
    show_toolbar=True,
    default_visible_columns=default_visible  # Use config value
)

layout.addWidget(self.transaction_table)
```

**Proposed Code (`_init_ui`):**
```python
# ... inside _init_ui
self.transaction_table = TableView()

# Get standard column widths from the central Columns registry
standard_widths = Columns.get_column_widths()

# Get default visible columns for the categorize module
default_columns = Columns.get_default_visible_columns('categorize')

# Get editable columns from the 'user_editable' group
editable_columns = [col.display_name for col in Columns.get('user_editable')]

# Configure the table with the specified settings
self.transaction_table.configure(
    auto_size_columns=True,
    max_column_width=40,
    column_widths=standard_widths,
    editable_columns=editable_columns,
    show_toolbar=True,
    default_visible_columns=default_columns
)

layout.addWidget(self.transaction_table)
```

#### B. In `set_transactions()` method:

The method will be simplified to only pass the dataframe to the table widget.

**Current Code (`set_transactions`):**
```python
# ... inside set_transactions
self.transaction_table.configure(
    auto_size_columns=True,
    max_column_width=40,
    column_widths=standard_widths,
    editable_columns=editable_columns,  # All user_editable columns
    show_toolbar=True,
    default_visible_columns=visible_columns if visible_columns else None
).set_dataframe(df).show()
```

**Proposed Code (`set_transactions`):**
```python
# ... inside set_transactions
self.transaction_table.set_dataframe(df)
```

## 3. Pros and Cons

-   **Pros:**
    -   **Significant Performance Gain:** This change will dramatically speed up the loading of transactions, directly addressing the identified bottleneck.
    -   **Correctness:** It aligns the code with the intended design, where configuration is a one-time setup action.
    -   **Low Risk:** The change is localized to a single class and is a straightforward refactoring.
-   **Cons:**
    -   None identified.

## 4. Recommendation

I recommend proceeding with this refactoring to resolve the performance issue.
