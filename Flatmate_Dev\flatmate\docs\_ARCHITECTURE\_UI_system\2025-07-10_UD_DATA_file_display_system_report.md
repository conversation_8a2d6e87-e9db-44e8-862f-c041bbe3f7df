# Report: File Metadata Display System (Resolved)

This document outlines the working system for displaying file metadata in the Update Data module's file browser.

## Executive Summary

The file metadata display issues are resolved. The system now correctly identifies file types and displays their size, bank, variant, and format in the UI. 

The logic path involves several components working in concert: the UI widget, a display helper, a handler registry, and individual statement handlers. While this creates a decoupled architecture, it can be complex to trace. This report documents the end-to-end data flow.

## The Data Flow: From Selection to Display

The process for identifying and displaying a file's metadata follows these steps:

1.  **File Selection (UI)**: The user adds files to the `FileDisplayWidget`. This widget is responsible for rendering the file list.
    -   **File**: `fm/modules/update_data/_view/center_panel/widgets/file_browser.py`

2.  **Information Request (Helper)**: The widget calls `FileDisplayHelper.get_file_info(file_path)` for each file to gather metadata.
    -   **File**: `fm/modules/update_data/_view/utils/file_display_helper.py`

3.  **Handler Discovery (Registry)**: The `FileDisplayHelper` calls `get_handler(file_path)` to find a suitable handler for the file.
    -   **File**: `fm/modules/update_data/utils/statement_handlers/_handler_registry.py`

4.  **Handler Matching (Base & Concrete Handlers)**: The `get_handler` function iterates through all registered handlers and calls `handler.can_handle_file(filepath)` on each. This method previews the file's content (e.g., headers, column count) to determine if it can process it.
    -   **Base Logic**: `fm/modules/update_data/utils/statement_handlers/_base_statement_handler.py`
    -   **Specific Logic**: Overridden in concrete handlers like `kiwibank_basic_csv_handler.py`.

5.  **Metadata Extraction (Helper)**: Once a matching handler is found, `get_file_info` accesses its `statement_type` attribute. This object contains the file's descriptive metadata.
    -   **Example Definition**: In `coop_standard_csv_handler.py`, `statement_type` is defined with `bank_name`, `variant`, and `file_type`.

6.  **Status String Creation (Helper)**: `get_file_info` uses the metadata from `statement_type` to create a single, pre-formatted `display_name` string (e.g., "Co-operative Bank standard csv").

7.  **UI Update (Widget)**: The `FileDisplayWidget` receives the info dictionary from the helper. Its `_add_file_item` method then sets the text for the 'Status' column directly from the `display_name` key.

## Conclusion

The system is now working as designed. The initial failures were caused by a combination of overly broad handler matching logic and a mismatch between the data keys provided by the helper and those expected by the UI. These issues have been resolved by:

-   Implementing stricter `can_handle_file` checks in the handlers.
-   Simplifying the data contract to use a single `display_name` for the status, which is prepared in the helper and consumed directly by the UI.
