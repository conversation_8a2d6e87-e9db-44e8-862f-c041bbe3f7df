# Window State Management in PySide6

## Overview

This document covers best practices for managing window states (maximize, restore, minimize) in PySide6 applications, particularly with frameless windows.

## The Challenge

Window state management in Qt applications involves several complexities:

1. **Event Timing** - `WindowStateChange` events may fire before actual state changes
2. **Multiple Events** - Single state changes can trigger multiple events
3. **Platform Differences** - Behavior varies between Windows, Linux, and macOS
4. **State Synchronization** - Keeping UI elements in sync with actual window state

## Common Issues

### 1. WindowStateChange Event Timing

```python
def changeEvent(self, event):
    if event.type() == QEvent.WindowStateChange:
        # This may report incorrect state immediately after showMaximized()
        maximized = self.isMaximized()  # ❌ May be False even after showMaximized()
```

**Problem**: Qt processes the visual change before updating the internal state.

### 2. Multiple Events for Single Action

```
DEBUG: WindowStateChange event - isMaximized=False
DEBUG: WindowStateChange event - isMaximized=False  # Duplicate!
DEBUG: WindowStateChange event - isMaximized=True   # Finally correct
```

**Problem**: Qt fires multiple events, some with incorrect state information.

### 3. State Desynchronization

```python
# User clicks maximize button
self.parent.showMaximized()  # Window visually maximizes
# But button state doesn't update because isMaximized() still returns False
```

## Solutions

### 1. Use QWindowStateChangeEvent for Better State Information

```python
from PySide6.QtGui import QWindowStateChangeEvent

def changeEvent(self, event):
    if event.type() == QEvent.WindowStateChange:
        # Cast to get old and new states
        state_event = QWindowStateChangeEvent(event)
        old_state = state_event.oldState()
        current_state = self.windowState()
        
        # Only update if maximize state actually changed
        was_maximized = bool(old_state & Qt.WindowState.WindowMaximized)
        is_maximized = bool(current_state & Qt.WindowState.WindowMaximized)
        
        if was_maximized != is_maximized:
            self.update_ui_for_state(is_maximized)
```

### 2. Implement Timeout Fallback

```python
def _toggle_maximize(self):
    if self.parent.isMaximized():
        self.parent.showNormal()
    else:
        self.parent.showMaximized()
    
    # Start timeout in case event doesn't arrive
    self._timeout_timer.start(500)

def _handle_timeout(self):
    # Force update based on actual state
    actual_state = self.parent.isMaximized()
    self.update_ui_for_state(actual_state)
```

### 3. Use Button State as Source of Truth

```python
def _toggle_maximize(self):
    # Use button appearance to determine action
    current_text = self.maximize_btn.text()
    is_showing_maximize = (current_text == "□")
    
    if is_showing_maximize:
        self.parent.showMaximized()
    else:
        self.parent.showNormal()
```

## Best Practices

### 1. Defer State Updates

```python
def changeEvent(self, event):
    if event.type() == QEvent.WindowStateChange:
        super().changeEvent(event)
        # Use timer to defer update
        QTimer.singleShot(0, self._update_window_controls_state)
```

### 2. Validate State Changes

```python
def update_for_state(self, maximized: bool):
    if self._pending_action:
        expected_state = (self._pending_action == "maximize")
        if maximized != expected_state:
            return  # Ignore spurious events
```

### 3. Handle Platform Differences

```python
if sys.platform == "darwin":  # macOS
    # macOS-specific handling
    self.setSystemTitleBarButtonVisible(True)
elif sys.platform == "win32":  # Windows
    # Windows-specific handling
    pass
```

## Recommended Approach: Use Proven Libraries

Instead of implementing custom window state management, use established libraries:

### PyQt-Frameless-Window

```python
from qframelesswindow import FramelessWindow

class MainWindow(FramelessWindow):
    def __init__(self):
        super().__init__()
        # Library handles all state management automatically
```

**Benefits**:
- Handles all timing issues
- Cross-platform compatibility
- Battle-tested edge case handling
- No custom event management needed

### QT-PyQt-PySide-Custom-Widgets

```json
{
    "QMainWindow": [{
        "frameless": true,
        "navigation": [{
            "restore": [{
                "buttonName": "restore_button",
                "normalIcon": "maximize.svg",
                "maximizedIcon": "restore.svg"
            }]
        }]
    }]
}
```

**Benefits**:
- JSON configuration
- Automatic state synchronization
- Built-in button state management

## Migration from Custom Implementation

### Step 1: Identify Current Issues
- Document all state synchronization problems
- Note platform-specific behaviors
- List all custom event handlers

### Step 2: Choose Library
- **PyQt-Frameless-Window**: Maximum flexibility, preserve custom design
- **QT-PyQt-PySide-Custom-Widgets**: Easy configuration, less customization

### Step 3: Gradual Migration
1. Install chosen library
2. Create test branch
3. Replace base class
4. Remove custom state management
5. Test thoroughly on all platforms

### Step 4: Cleanup
- Remove debugging code
- Remove custom event handlers
- Simplify button logic
- Update documentation

## Common Pitfalls

### 1. Checking State Too Early

```python
# ❌ Wrong - state may not be updated yet
self.parent.showMaximized()
if self.parent.isMaximized():  # May still be False
    self.update_button()
```

```python
# ✅ Correct - let event system handle it
self.parent.showMaximized()
# Update happens in changeEvent when state is actually changed
```

### 2. Not Handling Multiple Events

```python
# ❌ Wrong - updates on every event
def changeEvent(self, event):
    if event.type() == QEvent.WindowStateChange:
        self.update_button(self.isMaximized())
```

```python
# ✅ Correct - only update when state actually changes
def changeEvent(self, event):
    if event.type() == QEvent.WindowStateChange:
        state_event = QWindowStateChangeEvent(event)
        old_maximized = bool(state_event.oldState() & Qt.WindowState.WindowMaximized)
        new_maximized = bool(self.windowState() & Qt.WindowState.WindowMaximized)
        
        if old_maximized != new_maximized:
            self.update_button(new_maximized)
```

### 3. Platform Assumptions

```python
# ❌ Wrong - assumes Windows behavior
def _toggle_maximize(self):
    self.showMaximized()
    # Assumes immediate state change
```

```python
# ✅ Correct - platform-agnostic
def _toggle_maximize(self):
    if self.isMaximized():
        self.showNormal()
    else:
        self.showMaximized()
    # Let event system handle state updates
```

## Debugging Window State Issues

### Enable Detailed Logging

```python
def changeEvent(self, event):
    if event.type() == QEvent.WindowStateChange:
        import time
        timestamp = time.time()
        state_event = QWindowStateChangeEvent(event)
        
        print(f"[{timestamp:.3f}] WindowStateChange:")
        print(f"  Old state: {state_event.oldState()}")
        print(f"  Current state: {self.windowState()}")
        print(f"  isMaximized(): {self.isMaximized()}")
```

### Track Button Clicks

```python
def _on_maximize_clicked(self):
    import time
    print(f"[{time.time():.3f}] Button clicked - Current state: {self.parent.isMaximized()}")
    self._toggle_maximize()
```

### Monitor State Transitions

```python
def _toggle_maximize(self):
    old_state = self.parent.isMaximized()
    if old_state:
        self.parent.showNormal()
        expected = False
    else:
        self.parent.showMaximized()
        expected = True
    
    print(f"State transition: {old_state} -> {expected} (expected)")
```

## Conclusion

Window state management in Qt is complex due to timing issues and platform differences. The recommended approach is to use proven libraries like PyQt-Frameless-Window that handle these complexities automatically, rather than implementing custom solutions that are prone to edge cases and synchronization issues.
