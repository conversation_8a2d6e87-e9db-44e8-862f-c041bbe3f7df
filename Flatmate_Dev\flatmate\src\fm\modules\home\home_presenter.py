"""
Home presenter implementation for the FlatMate application.
"""

from PySide6.QtCore import QTimer
from PySide6.QtWidgets import QApplication

from ...core.services.logger import log
from ...gui.services.info_bar_service import InfoBarService
from ..base.base_presenter import Base<PERSON>resenter
from .home_content import WELCOME_CONTENT
from .home_state import HomeState
from .home_view import HomeView


class HomePresenter(BasePresenter):
    """Presenter for the home module."""

    def __init__(self, main_window, db_io_service, gui_config=None, gui_keys=None):
        """Initialize the home presenter.

        Args:
            main_window: The main window instance
            db_io_service: The shared database I/O service instance
            gui_config: Injected GUI configuration service
            gui_keys: Injected GUI configuration keys
        """
        # Call parent constructor with injected dependencies
        super().__init__(main_window, db_io_service, gui_config, gui_keys)

        # Initialize home-specific state
        self.state = HomeState()
        self.info_bar_service = InfoBarService.get_instance()

        log.debug("Home Presenter initialization complete")

    def _create_view(self):
        """Create the view instance. Called once during setup."""
        return HomeView(gui_config=self.gui_config, gui_keys=self.gui_keys)

    def _connect_signals(self):
        """Connect view signals to handlers. Called once during setup."""
        log.debug("Connecting Home View signals")
        # These will now request transitions through the coordinator
        self.view.update_data_clicked.connect(
            lambda: self.request_transition("update_data")
        )
        self.view.categorize_clicked.connect(lambda: self.request_transition("categorize"))
        self.view.settings_clicked.connect(lambda: self.request_transition("settings"))
        self.view.quit_clicked.connect(self._on_quit_click)

    def _refresh_content(self, **params):
        """Refresh home content when shown.

        This method is called every time the module becomes visible.
        It handles the first-run logic and content display.

        Args:
            **params: Optional parameters passed from navigation
        """
        log.debug("Refreshing Home content")

        if self.state.is_first_run:
            # First show the splash screen (logo only)
            self.view.show_splash_screen()
            # Then after a delay, show the welcome content
            QTimer.singleShot(
                2000, lambda: self.view.show_default_content(WELCOME_CONTENT)
            )
            self.state.mark_first_run_complete()
        else:
            # Just show the welcome content directly
            self.view.show_default_content(WELCOME_CONTENT)

        log.debug("Home content refresh complete")

    def _on_quit_click(self):
        """Handle Quit button click."""
        QApplication.quit()

    # Note: request_transition is inherited from BasePresenter
    # Note: cleanup is inherited from BasePresenter
