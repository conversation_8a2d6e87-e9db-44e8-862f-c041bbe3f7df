# Notes

11:04 PM, 18 Jun 2025
okay so about this pyside documentation  i see its half done .. what remains ?
We have sometimes used a python layer to create classes of objects
like the re usable widgets in gui _shared_components
(the underscore is also inconsistently applied, usuallyjust to get things sitting in the file tree nicely .. kind of rediculous ...

@c:\Users\<USER>\_DEV\__PROJECTS/Flatmate_Dev\flatmate\src\fm\gui\_shared_components/ but the @ system doesnt seem to mind

12:27 AM, 18 Jun 2025

tidy up left panel buttons in categorize - should be using predefined types - either in shared components - or the qss types ( not discoverable by type hints unfortunately )

12:18 AM, 18 Jun 2025

notes : it may be worth checking whetehr there is a base class in gui shared / components or bases that handles cleaning up signals etc these signals issues ..  that could be a thought for another day ...

(re clenaing up gui shared ui components)

01:50 PM, 17 Jun 2025

long term, lose left panel, modify home module... which becomes dash board, the entry point for the app, on first run the app checks if there is anything in the database, if not the app opens at update_data
which could be called "import_data" or get data, or import transactions or get transactions,

## cat_trans module

- (trans_cat?)
- need reizable columns that open at sensible defaults and remember last setting (module config)
- need side panels to be collapsible
- need to be able to add new tags to transactions
- can manually edit categories
- should be a drop dwon list in the cell with -- add new category - when clicked makes the cell editable, with active cursor in cell, this becomes the new category -saves context changing
- Cell rows should be highlighted when clicked
- Instead of two buttons - left panel should say
  label: load transactions
  option menu - from file
  - from database

After loading there should be an option to
 label: filter by:
 Option menu: by date range
            - by category
            - by account
            - by tag
            - by amount range
            - by description
            - by notes

 Alternatively or as well, - there could be an option to query the data base for a specific date range in the first place - and then the filters would be applied to that subset

### I note there is a "transaction ID"

 which is simply numbered 1 to n in the order they appear in the database.

 How does this work when new transaztions are added ? simply reassigned ?

#### *important* - some fomrats have a unique transaction ID - in this format, this is the only means of being one hundred percent sure a transaction is a duplicate - because tthere is no balance

- I note that in the current rendering no balance is present - must check if it is in the actual db data
  -We need a show all option
- and or a select collumsn to view option -(check boxes-dynamic -based on current database schema)
- perhaps an option to add custom columns

### We need an option to export the current view to csv or xlsx (excell format)

### we need to consider the default column order - its supposed to be based on the order in standards - meaning changing the order their should chage the default order in the data base and here

### currently no ability to select an entire row and copy it

## we may need to build a custom view table to be used throughout the app

pref with a light and dark mode
ultimately should be customisable or have themes
For now, we will need to break this out of cat_view.py
and into a view_components folder (naming convention)

## 2025-06-19 @ 15:59:55 4:00 pm

todo:
create a tool bar base class
we want a tool bar that can be placed anywhere easily
can be vertical or horizontal
contains different tools
these tools will contain widgets and utils

- we will want a script tha creates a new tool bar from a template
- it will contain a scripts folder, that folder will create new tools
  folder structure, (empty files classes inheriting the proper base classes)

2025-06-22 @ 20:52:15

We need to sort out pipeline with a way to handle fm_Master_Files

2025-06-22 @ 21:37:03

- consider rolling all data related servies into core.data_services

including the sql logic - db_io service
fm_standard_columns
date_service
column_manager (?name?)

## 2025-06-28 @ 22:26:11

## Dev_notes : update_data module the gui does not remember my preference for file selection method - FIXED

2025-06-28 @ 23:39:22

still wrestling with update_data errors - updating the database, after recent refactoing of the statement handling system

- one minor note, i'm not getting the "export_result" in my update_data module ui ..
- odd message in the status bar Error: proceessing complete

2025-06-29 @ 04:09:21
must do something about these fricking stupid empty folers popping up throughout the app .. they must be being created somewhere at run time

2025-06-29 @ 15:41:59

We are gong to need to create different classes of columns
Statement columns
import_metadata_columns
DB_columns
user_modified_columns

Column sets:
All_columns
Required_columns
Default_visible_columns / Core Columns
Basic_columns

The question is how best to structure this schema

We also need to provide the capacity for users to
Name their accounts - and have the names displayed
Perhaps in a seperate column - account_name
this could be displayed instead of the acocunt number by default

![*************](image/__DEV_NOTES/*************.png)

2025-07-09 @ 17:04:53
I have a nagging question about achitecture
Should we be using the "database names" eg op_account, credit_amount,  in all cases other than for display or output?
or, do we use the capitalised names in all cases, and convert only for the datbase...?

- and I note that the asbbank format does in fact have an mount column - which renders unique ID near useless for our purposes.
  (perhaps rename "source_uid")
  2 transactions with the same details on the same day, could never have the same amount ...
  which should ensure no duplicates.
  however we'll leave it for now
  Could come in handy if available

2025-07-09 @ 17:25:52

We have simplified the logic in the base statement handler. I am unsure as to why we had so many issues..
As I suspected, we simple read all csvs as headless
and if colnames_in_header is true
col_names_row = 0
else
if has_col_names = false we handle accordingly
(assign collunmanes form target_cols in handler)
if true
col_name_row must be specified.

I have nagging issues about thie "context_manager" function
Which is to do with logging errors from the statement handlers

Right now I need a working prototype to actually use for my own purposes ...

WE are going to have to tackle this collumns data structure..
WE HAVE MUCH DISCUSSION ON THE ISSUE, WE NEED TO PICK A solution and migrate to it ..
At the moment, the csv output for individual handlers looks good

next we need to take a look at the pipline.

## 2025-07-10 @ 13:31:51

We have made some changes to rationalise statement handling and are working throught pipeline and director in ud data - (AGAIN FFS)

it occured ot me we should have credit and debit columns created for dfs
they can be populated from the amount column

Have also been pondering extracting the date created from the filename, which seems to be in most formats  - and if not, from the last transaction date in the df

As in a designated _add_fm_metadata_cols in the base statement handler

Also consiering keeping original column names where they dont cleanly map to our standard columns - this could lead to large numbers of collumn names - they could be handled by a dataclass - original_columns

### the question of the column handling system still needs to be addressed

everything currently relies on a the very simple fm standard columns enum..
this is a system wide issue - we could retain it and slowly migrate, and we could make the new data classes refer to it also ...

### make tidying the source folder optional !

(job sheet now can have flag in director for whether to tidy files or not)

### add check boxes to update data

- tidy files
- create master

---

# ------------------- MILESTONE ACHIEVED -------------------

## final commit for "refactoring-statement-handlers" branch -

---

## 2025-07-10 @ 13:31:51

 Evrything basically funcitonal ...
 Statements working - database working

A lot if work todo.
More complete metadata on import
Tidy up update data gui

add options - create master csv
add options - tidy files
(check boxes)
have them remember last setting

Account filtering for categorize module  - and  ACTUAL CATEGORIZING

nav bar icon link to categorise also

The app should open at the last used module

Config system has two competing systems one in categorise with its base in core.config
and the older versions in update dat and home, and core _config, and GUI using the config_keys system.
###  But the final commit for the *refactoring-statement-handlers* branch is funcitonal. 

# NEW BRANCH :  "cat-transactions_mvp-for-personal-use"

- just work on what I need right now. for my own use ..
- so I shifted the database into a backup folder 
- the app should create a new data base ?
## Should alert - no database found - create new database ?

the app should create a new data base 
( i think ) anyway I tried to update the data base and I got a weird error.
Which I will try to recreate.
## Also I've noticed some very odd bheaviour on resize.
/ restore down and expand / full screen.. It doesent resize down to a sensible default.

# 2025-07-11 @ 21:23:05

## Consider: Renaming Columns dataclass to Std_Columns

to distinguish from handler collum attrs

2025-07-12 @ 13:31:45

## Date handling issues and column thoughts

- it occurs to me that we *may* want to bring back a dynamic column reasignment system
for when source column names are available 
(has_col_names=True)
Right now focusing on dates.. 

also keen to change generic naming of data_services.Columns to something more specific

## High priority - rationalise our custom logger [x]
- method is now simply log.info("message")
example usage options:

```python
log.info("message")
log.debug("message")
log.warning("message")
log.error("message") # default: exc_info=True (can be overriden)
log.critical("message") # default: exc_info=True (can be overriden)
``` 
currently: (I think)
config parameter debug = True (verbose terminal output)
                      = False (only show warnings and errors and criticals)

#                      