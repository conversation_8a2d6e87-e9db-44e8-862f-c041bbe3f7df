# Header Handling Standardization Proposal

## Current Issues

1. **Inconsistent Loading**
   - Some files are loaded with headers, others without
   - Handlers make assumptions about header presence
   - Complex fallback logic in file loading

2. **Tight Coupling**
   - File loader makes decisions about file format
   - Handlers must adapt to loader's behavior
   - Hard to add new statement formats

## Proposed Solution: Headerless by Default

### Core Principle
- **All CSVs are loaded without headers** (`header=None`)
- **Statement handlers define their header structure**
- **No magic behavior** - explicit is better than implicit

### Implementation Details

#### 1. File Loading (`file_utils.py`)
```python
def load_csv_to_df(filepath: str) -> pd.DataFrame:
    """Load CSV file without headers."""
    return pd.read_csv(filepath, header=None)
```

#### 2. Base Handler Updates (`_base_statement_handler.py`)
```python
@dataclass
class StatementHandler:
    # Current column attributes
    column_attrs: ColumnAttributes
    
    # Add header row specification
    header_row: int = 0  # Default to first row
    skip_rows_after_header: int = 0  # Rows to skip after header
    
    def get_header_rows(self) -> List[int]:
        """Return list of rows containing header information."""
        return [self.header_row] if self.column_attrs.has_col_names else []
    
    def get_skip_rows(self) -> List[int]:
        """Return list of rows to skip (metadata, etc.)."""
        rows = []
        if self.source_metadata_attrs.has_metadata_rows:
            rows.extend(range(
                self.source_metadata_attrs.metadata_start[0],
                self.source_metadata_attrs.metadata_end[0] + 1
            ))
        return rows
```

#### 3. Handler Implementation Example (`kiwibank_full_csv_handler.py`)
```python
def __post_init__(self):
    self.statement_format = self.StatementFormat(
        bank_name="Kiwibank",
        variant="full",
        file_type="csv",
    )
    
    # Define header as row 0
    self.header_row = 0
    
    # Column definitions remain the same
    self.column_attrs = self.ColumnAttributes(
        has_col_names=True,
        source_col_names=[
            "Account number", "Date", "Memo/Description", 
            # ... other columns ...
        ],
        # ... other attributes ...
    )
```

#### 4. Handler Registry Updates (`_handler_registry.py`)
```python
def load_and_validate(handler: StatementHandler, filepath: str) -> Optional[pd.DataFrame]:
    """Load file and validate using handler's specifications."""
    try:
        # Load raw data
        df = pd.read_csv(
            filepath,
            header=None,
            skiprows=handler.get_skip_rows(),
            skip_blank_lines=False
        )
        
        # Extract and validate headers if needed
        if handler.column_attrs.has_col_names:
            headers = df.iloc[handler.get_header_rows()]
            # Validate headers against handler's expected columns
            if not handler._validate_headers(headers):
                return None
            
            # Remove header rows from data
            df = df.drop(handler.get_header_rows())
            
        # Reset index after removing rows
        df = df.reset_index(drop=True)
        
        # Add metadata
        df.attrs.update({
            'filepath': filepath,
            'filename': os.path.basename(filepath)
        })
        
        return df
        
    except Exception as e:
        log(f"Error loading {filepath}: {str(e)}", level="error")
        return None
```

## Architectural Rationale

This proposal addresses a core architectural flaw in the current system.

- **Correcting Responsibility**: The fundamental issue is that the generic `file_utils.py` loader was responsible for understanding the structure of specific bank statements. This violates the Single Responsibility Principle. The proposed solution corrects this by implementing **Inversion of Control**: the `StatementHandler`, which is the expert on a given format, dictates how the file should be parsed.

- **Explicit Over Implicit**: The previous "try-catch" loading mechanism was implicit and relied on "magic" behavior that was hard to debug. This new approach is explicit. Each handler's configuration becomes self-documenting, clearly stating the file structure it expects.

- **Robustness and Maintainability**: By isolating format-specific logic within each handler, the system becomes more robust and easier to maintain. Adding support for a new bank becomes a self-contained task without modifying central loading logic.

## Impact on Downstream Systems (e.g., `dw_pipeline`)

This change will **not** negatively affect downstream systems like the `dw_pipeline`, provided the handlers are implemented correctly. The `StatementHandler` acts as a critical abstraction and transformation layer.

- **The Handler's Contract**: The role of the handler is to consume a raw, potentially messy file (loaded "headless") and produce a **standardized DataFrame**.
- **Standardized Output**: This standardized DataFrame is what gets passed to the rest of the system. It will have the correct column names (e.g., `Date`, `Details`, `Amount`), data types, and will have all metadata/header rows removed.
- **Shielding the Pipeline**: The `dw_pipeline` and other services only ever interact with this clean, standardized data. They are completely shielded from the initial raw loading format. The `validate_core_data_structure` function within the pipeline confirms this, as it would fail if it ever received a headless DataFrame.

## Benefits

1. **Simpler Code**
   - No more complex fallback logic
   - Clear separation of concerns
   - Easier to debug

2. **More Reliable**
   - Consistent behavior across all file types
   - Explicit header handling
   - Better error messages

3. **Easier Maintenance**
   - Handlers are self-documenting
   - Adding new formats is straightforward
   - No hidden behavior

## Migration Path

1. Update base handler with new header handling
2. Modify handlers one by one
3. Update tests for each handler
4. Remove old loading logic

## Testing Strategy

1. **Unit Tests**
   - Test each handler's header detection
   - Verify skip row logic
   - Check error cases

2. **Integration Tests**
   - Test with real statement files
   - Verify backward compatibility
   - Check performance impact
