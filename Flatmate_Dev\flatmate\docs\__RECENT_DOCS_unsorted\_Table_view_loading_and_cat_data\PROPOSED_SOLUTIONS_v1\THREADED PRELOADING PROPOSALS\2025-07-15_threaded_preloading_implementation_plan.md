# Implementation Plan: Threaded Table Preloading

**Objective:** Eliminate the ~3-second UI freeze when navigating to the Categorize module by moving the expensive data preparation to a background thread at application startup.

---

## Step 1: Create the `DataPreparationService`

This new service will manage the background processing. It uses a `QRunnable` worker to perform the data preparation on a global `QThreadPool` without blocking the main UI thread.

**File:** `flatmate/src/fm/core/services/data_preparation_service.py`

```python
"""
Service for preparing data in the background to improve UI responsiveness.
"""
from __future__ import annotations

import pandas as pd
from PySide6.QtCore import QObject, Signal, QRunnable, QThreadPool

from .logger import log
from ..data_services import DBIOService
from ...modules.categorize.core.categorizer import TransactionCategorizer


class DataPreparationWorker(QRunnable):
    """Worker to prepare data in a background thread."""

    def __init__(self, data_service: DBIOService, categorizer: TransactionCategorizer):
        super().__init__()
        self.data_service = data_service
        self.categorizer = categorizer
        self.signals = self.WorkerSignals()

    class WorkerSignals(QObject):
        finished = Signal(object)  # Emits the prepared DataFrame
        error = Signal(str)

    def run(self):
        """The main work of the thread."""
        try:
            log.info("Background Worker: Starting data preparation for Categorize module.")
            # 1. Fetch raw data from the cache
            df = self.data_service.get_transactions_dataframe()
            if df.empty:
                log.warning("Background Worker: No transactions found in cache. Nothing to prepare.")
                self.signals.finished.emit(df)
                return

            # 2. Apply categorization
            log.info(f"Background Worker: Applying categorization to {len(df)} transactions...")
            df["category"] = df.apply(self.categorizer.categorize_row, axis=1)

            # 3. Apply default sorting
            if 'date' in df.columns:
                df = df.sort_values(by='date', ascending=False)

            log.info("Background Worker: Data preparation complete.")
            self.signals.finished.emit(df)

        except Exception as e:
            log.error(f"Background Worker: Error during data preparation: {e}")
            self.signals.error.emit(str(e))


class DataPreparationService:
    """Singleton service to manage pre-loading and preparation of data."""
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(DataPreparationService, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if self._initialized:
            return
        self.thread_pool = QThreadPool.globalInstance()
        self.categorize_df: pd.DataFrame | None = None
        self.is_ready = False
        self.data_service = DBIOService()
        self.categorizer = TransactionCategorizer()
        self._initialized = True

    def start_preparation(self):
        """Starts the background data preparation for all necessary modules."""
        log.info("DataPreparationService: Kicking off background data preparation.")
        worker = DataPreparationWorker(self.data_service, self.categorizer)
        worker.signals.finished.connect(self._on_preparation_finished)
        worker.signals.error.connect(self._on_preparation_error)
        self.thread_pool.start(worker)

    def _on_preparation_finished(self, df: pd.DataFrame):
        """Callback when the worker has finished."""
        log.info("DataPreparationService: Successfully received prepared data.")
        self.categorize_df = df
        self.is_ready = True

    def _on_preparation_error(self, error_message: str):
        """Callback when the worker encounters an error."""
        log.error(f"DataPreparationService: Failed to prepare data: {error_message}")
        self.categorize_df = None
        self.is_ready = False

    def get_categorize_dataframe(self) -> pd.DataFrame | None:
        """Returns the prepared DataFrame for the Categorize module."""
        return self.categorize_df


# Create a singleton instance
data_preparation_service = DataPreparationService()
```

---

## Step 2: Integrate the Service into the Application

### A. Make the Service Importable

Modify the `__init__.py` file to include the new service.

**File:** `flatmate/src/fm/core/services/__init__.py`

```python
# Target Content
from .event_bus import global_event_bus, Events
from .logger import log
from .cache_service import cache_service

# Replacement Content
from .event_bus import global_event_bus, Events
from .logger import log
from .cache_service import cache_service
from .data_preparation_service import data_preparation_service
```

### B. Start the Service at Application Launch

Modify `main.py` to kick off the background data preparation after the UI is visible and the database cache is loaded.

**File:** `flatmate/src/fm/main.py`

```python
# Add this import
from .core.services import data_preparation_service

# ... inside initialize_application() ...

# Find this section:
        # 7. Start coordinator
        coordinator.start()  # Start coordinator to transition to home

# Replace with this:
        # 7. Start background data preparation
        log.info("\n=== Starting Background Data Preparation ===")
        data_preparation_service.start_preparation()

        # 8. Start coordinator
        coordinator.start()  # Start coordinator to transition to home
```

---

## Step 3: Refactor `CategorizePresenter` to Use Pre-processed Data

This is the final step. The presenter's logic is drastically simplified. Instead of performing the complex data processing itself, it just retrieves the finished DataFrame from the service.

**File:** `flatmate/src/fm/modules/categorize/cat_presenter.py`

### A. Add the new import:

```python
from ...core.services import data_preparation_service
```

### B. Replace the `_handle_load_db` method:

```python
# Find this entire method and replace it:
    @timing_decorator
    def _handle_load_db(self, filters=None):
        # ... (the entire old, long method) ...

# Replace it with this new, simplified version:
    @timing_decorator
    def _handle_load_db(self, filters=None):
        """Load pre-processed data from the DataPreparationService."""
        import time
        log.info("Fetching pre-processed data for categorisation...")
        start_time = time.time()

        self.info_bar_service.publish_loading("Loading transactions...")

        try:
            if not data_preparation_service.is_ready:
                log.warning("Data preparation service is not ready yet. Waiting...")
                self.info_bar_service.publish_warning("Preparing data, please wait...")
                time.sleep(2) # Simplified wait

            with timing_context("Data Retrieval from Pre-processed Cache"):
                df = data_preparation_service.get_categorize_dataframe()

            if df is None or df.empty:
                log.warning("No pre-processed data available. The view will be empty.")
                self.info_bar_service.publish_warning("No transactions to display.")
                df = pd.DataFrame(columns=[
                    'date', 'details', 'amount', 'balance', 'account',
                    'source_uid', 'category', 'tags', 'notes', 'is_processed'
                ])
                self.view.set_dataframe(df)
                return

            with timing_context("Table View Data Setting"):
                log(f"Setting pre-processed DataFrame with {len(df)} transactions to view")
                self._original_df = df.copy()
                self.view.set_dataframe(df)
                self._modified = False

            elapsed = time.time() - start_time
            log(f"Successfully displayed {len(df)} pre-processed transactions in {elapsed:.3f}s", level="info")

            self.info_bar_service.publish_message(
                f"Displayed {len(df)} transactions",
                is_loading=False
            )

        except Exception as e:
            error_msg = f"Error loading pre-processed transactions: {str(e)}"
            log.error(error_msg, level="error")
            self.info_bar_service.publish_error(error_msg)
            raise
```
